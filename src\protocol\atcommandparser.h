#ifndef ATCOMMANDPARSER_H
#define ATCOMMANDPARSER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QRegularExpression>
#include <QTimer>
#include <QQueue>
#include "atcommand.h"
#include "atresponse.h"

/**
 * @brief AT指令解析器接口
 */
class IAtCommandParser
{
public:
    virtual ~IAtCommandParser() = default;

    /**
     * @brief 解析AT指令
     * @param command 原始指令字符串
     * @return AT指令对象
     */
    virtual AtCommand parseCommand(const QString& command) = 0;

    /**
     * @brief 解析AT响应
     * @param response 原始响应字符串
     * @return AT响应对象
     */
    virtual AtResponse parseResponse(const QString& response) = 0;

    /**
     * @brief 格式化AT指令
     * @param command AT指令对象
     * @return 格式化后的指令字符串
     */
    virtual QString formatCommand(const AtCommand& command) = 0;

    /**
     * @brief 格式化AT响应
     * @param response AT响应对象
     * @return 格式化后的响应字符串
     */
    virtual QString formatResponse(const AtResponse& response) = 0;

    /**
     * @brief 验证指令有效性
     * @param command AT指令对象
     * @return 有效返回true
     */
    virtual bool validateCommand(const AtCommand& command) = 0;
};

/**
 * @brief AT指令解析器实现类
 */
class AtCommandParser : public QObject, public IAtCommandParser
{
    Q_OBJECT

public:
    explicit AtCommandParser(QObject *parent = nullptr);
    ~AtCommandParser();

    // 实现IAtCommandParser接口
    AtCommand parseCommand(const QString& command) override;
    AtResponse parseResponse(const QString& response) override;
    QString formatCommand(const AtCommand& command) override;
    QString formatResponse(const AtResponse& response) override;
    bool validateCommand(const AtCommand& command) override;

    /**
     * @brief 处理接收到的数据
     * @param data 接收到的原始数据
     */
    void processReceivedData(const QByteArray& data);

    /**
     * @brief 设置行结束符
     * @param lineEnding 行结束符
     */
    void setLineEnding(const QString& lineEnding);

    /**
     * @brief 获取行结束符
     * @return 行结束符
     */
    QString getLineEnding() const;

    /**
     * @brief 清空缓冲区
     */
    void clearBuffer();

    /**
     * @brief 设置响应超时时间
     * @param timeout 超时时间(毫秒)
     */
    void setResponseTimeout(int timeout);

    /**
     * @brief 获取响应超时时间
     * @return 超时时间(毫秒)
     */
    int getResponseTimeout() const;

signals:
    /**
     * @brief 解析到完整指令信号
     * @param command AT指令对象
     */
    void commandParsed(const AtCommand& command);

    /**
     * @brief 解析到完整响应信号
     * @param response AT响应对象
     */
    void responseParsed(const AtResponse& response);

    /**
     * @brief 解析到URC信号
     * @param response URC响应对象
     */
    void urcReceived(const AtResponse& response);

    /**
     * @brief 解析错误信号
     * @param error 错误信息
     */
    void parseError(const QString& error);

private slots:
    /**
     * @brief 处理缓冲区超时
     */
    void onBufferTimeout();

private:
    /**
     * @brief 处理完整的行数据
     * @param line 行数据
     */
    void processLine(const QString& line);

    /**
     * @brief 检查是否为AT指令
     * @param line 行数据
     * @return 是AT指令返回true
     */
    bool isAtCommand(const QString& line) const;

    /**
     * @brief 检查是否为AT响应
     * @param line 行数据
     * @return 是AT响应返回true
     */
    bool isAtResponse(const QString& line) const;

    /**
     * @brief 检查是否为URC
     * @param line 行数据
     * @return 是URC返回true
     */
    bool isUrc(const QString& line) const;

    /**
     * @brief 清理字符串
     * @param str 输入字符串
     * @return 清理后的字符串
     */
    QString cleanString(const QString& str) const;

private:
    QString m_lineEnding;                   // 行结束符
    QByteArray m_buffer;                    // 接收缓冲区
    QTimer* m_bufferTimer;                  // 缓冲区超时定时器
    int m_responseTimeout;                  // 响应超时时间

    // 正则表达式
    QRegularExpression m_atCommandRegex;    // AT指令正则
    QRegularExpression m_atResponseRegex;   // AT响应正则
    QRegularExpression m_urcRegex;          // URC正则

    /**
     * @brief 初始化正则表达式
     */
    void initRegexPatterns();
};

#endif // ATCOMMANDPARSER_H
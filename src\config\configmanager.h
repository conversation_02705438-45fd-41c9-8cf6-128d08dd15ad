#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QMutex>
#include "appconfig.h"

/**
 * @brief 配置管理器类 - 单例模式
 *
 * 负责应用程序配置的加载、保存和管理
 * 支持JSON格式配置文件的读写
 * 线程安全的配置访问
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取配置管理器单例实例
     * @return ConfigManager实例指针
     */
    static ConfigManager* instance();

    /**
     * @brief 销毁单例实例
     */
    static void destroyInstance();

    /**
     * @brief 加载配置文件
     * @param filePath 配置文件路径
     * @return 加载成功返回true，失败返回false
     */
    bool loadConfig(const QString& filePath = "config/config.json");

    /**
     * @brief 保存配置到文件
     * @param filePath 配置文件路径
     * @return 保存成功返回true，失败返回false
     */
    bool saveConfig(const QString& filePath = "config/config.json");

    /**
     * @brief 获取应用程序配置
     * @return 应用程序配置结构体
     */
    AppConfig getConfig() const;

    /**
     * @brief 设置应用程序配置
     * @param config 应用程序配置结构体
     */
    void setConfig(const AppConfig& config);

    /**
     * @brief 获取串口配置
     * @return 串口配置结构体
     */
    SerialConfig getSerialConfig() const;

    /**
     * @brief 设置串口配置
     * @param config 串口配置结构体
     */
    void setSerialConfig(const SerialConfig& config);

    /**
     * @brief 获取日志配置
     * @return 日志配置结构体
     */
    LogConfig getLogConfig() const;

    /**
     * @brief 设置日志配置
     * @param config 日志配置结构体
     */
    void setLogConfig(const LogConfig& config);

    /**
     * @brief 获取MQTT配置
     * @return MQTT配置结构体
     */
    MqttConfig getMqttConfig() const;

    /**
     * @brief 设置MQTT配置
     * @param config MQTT配置结构体
     */
    void setMqttConfig(const MqttConfig& config);

    /**
     * @brief 重置为默认配置
     */
    void resetToDefault();

    /**
     * @brief 验证配置有效性
     * @return 配置有效返回true，无效返回false
     */
    bool validateConfig() const;

signals:
    /**
     * @brief 配置变更信号
     */
    void configChanged();

    /**
     * @brief 配置加载完成信号
     * @param success 加载是否成功
     */
    void configLoaded(bool success);

private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 禁用拷贝构造和赋值操作
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;

    /**
     * @brief 从JSON对象加载配置
     * @param jsonObj JSON对象
     */
    void loadFromJson(const QJsonObject& jsonObj);

    /**
     * @brief 将配置保存到JSON对象
     * @return JSON对象
     */
    QJsonObject saveToJson() const;

    /**
     * @brief 加载串口配置
     * @param jsonObj JSON对象
     */
    void loadSerialConfig(const QJsonObject& jsonObj);

    /**
     * @brief 加载日志配置
     * @param jsonObj JSON对象
     */
    void loadLogConfig(const QJsonObject& jsonObj);

    /**
     * @brief 加载MQTT配置
     * @param jsonObj JSON对象
     */
    void loadMqttConfig(const QJsonObject& jsonObj);

    /**
     * @brief 保存串口配置到JSON
     * @return JSON对象
     */
    QJsonObject saveSerialConfig() const;

    /**
     * @brief 保存日志配置到JSON
     * @return JSON对象
     */
    QJsonObject saveLogConfig() const;

    /**
     * @brief 保存MQTT配置到JSON
     * @return JSON对象
     */
    QJsonObject saveMqttConfig() const;

private:
    static ConfigManager* m_instance;          // 单例实例
    static QMutex m_mutex;                     // 线程安全锁

    AppConfig m_config;                        // 应用程序配置
    QString m_configFilePath;                  // 配置文件路径
    mutable QMutex m_configMutex;              // 配置访问锁
};

#endif // CONFIGMANAGER_H
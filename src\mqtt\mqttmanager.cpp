#include "mqttmanager.h"
#include <QMutexLocker>
#include <QDebug>

MqttManager::MqttManager(QObject *parent)
    : QObject(parent)
    , m_mqttClient(nullptr)
    , m_isInitialized(false)
    , m_queueTimer(nullptr)
    , m_retryTimer(nullptr)
    , m_retryEnabled(true)
    , m_maxRetries(3)
    , m_retryInterval(1000)
    , m_maxQueueSize(100)
{
    m_mqttClient = new MqttClient(this);
    m_queueTimer = new QTimer(this);
    m_retryTimer = new QTimer(this);

    m_queueTimer->setSingleShot(false);
    m_queueTimer->setInterval(100); // 100ms处理一次队列

    m_retryTimer->setSingleShot(true);

    // 连接MQTT客户端信号
    connect(m_mqttClient, &MqttClient::connectionStateChanged,
            this, &MqttManager::onMqttConnectionStateChanged);
    connect(m_mqttClient, &MqttClient::messageReceived,
            this, &MqttManager::onMqttMessageReceived);
    connect(m_mqttClient, &MqttClient::messagePublished,
            this, &MqttManager::onMqttMessagePublished);
    connect(m_mqttClient, &MqttClient::subscribed,
            this, &MqttManager::onMqttSubscribed);
    connect(m_mqttClient, &MqttClient::unsubscribed,
            this, &MqttManager::onMqttUnsubscribed);
    connect(m_mqttClient, &MqttClient::errorOccurred,
            this, &MqttManager::onMqttError);

    // 连接定时器
    connect(m_queueTimer, &QTimer::timeout,
            this, &MqttManager::processMessageQueue);
    connect(m_retryTimer, &QTimer::timeout,
            this, &MqttManager::onRetryTimeout);
}

MqttManager::~MqttManager()
{
    stop();
}

bool MqttManager::initialize(const MqttConfig& config)
{
    QMutexLocker locker(&m_mutex);

    m_config = config;

    // 配置MQTT客户端
    m_mqttClient->setConfiguration(config);
    m_mqttClient->setAutoReconnect(true, 5000);

    m_isInitialized = true;

    qInfo() << "MQTT管理器初始化成功:"
            << "主机:" << config.host
            << "端口:" << config.port
            << "客户端ID:" << config.clientId;

    return true;
}

bool MqttManager::start()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isInitialized) {
        setLastError("MQTT管理器未初始化");
        return false;
    }

    // 启动消息队列处理
    m_queueTimer->start();

    qInfo() << "MQTT管理器已启动";
    return true;
}

void MqttManager::stop()
{
    QMutexLocker locker(&m_mutex);

    if (m_queueTimer && m_queueTimer->isActive()) {
        m_queueTimer->stop();
    }

    if (m_retryTimer && m_retryTimer->isActive()) {
        m_retryTimer->stop();
    }

    if (m_mqttClient) {
        m_mqttClient->disconnectFromHost();
    }

    clearMessageQueue();

    qInfo() << "MQTT管理器已停止";
}

bool MqttManager::isConnected() const
{
    return m_mqttClient && m_mqttClient->isConnected();
}

bool MqttManager::connectToHost(const QString& host, quint16 port)
{
    if (!m_mqttClient) {
        setLastError("MQTT客户端未初始化");
        return false;
    }

    return m_mqttClient->connectToHost(host, port);
}

void MqttManager::disconnect()
{
    if (m_mqttClient) {
        m_mqttClient->disconnectFromHost();
    }
}

bool MqttManager::subscribe(const QString& topic, MqttQoS qos)
{
    if (!m_mqttClient) {
        setLastError("MQTT客户端未初始化");
        return false;
    }

    return m_mqttClient->subscribe(topic, qos);
}

bool MqttManager::unsubscribe(const QString& topic)
{
    if (!m_mqttClient) {
        setLastError("MQTT客户端未初始化");
        return false;
    }

    return m_mqttClient->unsubscribe(topic);
}

bool MqttManager::publish(const QString& topic, const QByteArray& payload,
                         MqttQoS qos, bool retain)
{
    MqttMessage message(topic, payload, qos, retain);
    return publish(message);
}

bool MqttManager::publish(const MqttMessage& message)
{
    QMutexLocker locker(&m_mutex);

    if (!m_mqttClient) {
        setLastError("MQTT客户端未初始化");
        return false;
    }

    if (message.topic.isEmpty()) {
        setLastError("主题名称不能为空");
        return false;
    }

    // 如果已连接，直接发送
    if (isConnected()) {
        bool success = m_mqttClient->publish(message.topic, message.payload,
                                           message.qos, message.retain);
        if (success) {
            return true;
        }
    }

    // 如果发送失败或未连接，加入队列
    enqueueMessage(message);
    return true; // 加入队列成功
}

IMqttClient* MqttManager::getMqttClient() const
{
    return m_mqttClient;
}

MqttConnectionState MqttManager::getConnectionState() const
{
    return m_mqttClient ? m_mqttClient->getConnectionState() : MqttConnectionState::Disconnected;
}

QString MqttManager::lastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void MqttManager::setMessageRetry(bool enabled, int maxRetries, int retryInterval)
{
    QMutexLocker locker(&m_mutex);
    m_retryEnabled = enabled;
    m_maxRetries = maxRetries;
    m_retryInterval = retryInterval;

    qInfo() << "MQTT消息重试设置:" << (enabled ? "启用" : "禁用")
            << "最大重试次数:" << maxRetries
            << "重试间隔:" << retryInterval << "ms";
}

void MqttManager::setMessageQueueSize(int maxSize)
{
    QMutexLocker locker(&m_mutex);
    m_maxQueueSize = maxSize;

    // 如果当前队列超过新的最大大小，移除多余的消息
    while (m_messageQueue.size() > m_maxQueueSize) {
        m_messageQueue.dequeue();
    }

    qInfo() << "MQTT消息队列大小设置:" << maxSize;
}

QStringList MqttManager::getSubscribedTopics() const
{
    return m_mqttClient ? m_mqttClient->getSubscribedTopics() : QStringList();
}

int MqttManager::getPendingMessageCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_messageQueue.size() + m_pendingMessages.size();
}

void MqttManager::onMqttConnectionStateChanged(MqttConnectionState state)
{
    qInfo() << "MQTT连接状态变化:" << static_cast<int>(state);

    if (state == MqttConnectionState::Connected) {
        emit connected();
        // 连接成功后处理队列中的消息
        processMessageQueue();
    } else if (state == MqttConnectionState::Disconnected) {
        emit disconnected();
    }

    emit connectionStateChanged(state);
}

void MqttManager::onMqttMessageReceived(const QString& topic, const QByteArray& payload)
{
    qInfo() << "收到MQTT消息 - 主题:" << topic << "大小:" << payload.size();
    emit messageReceived(topic, payload);
}

void MqttManager::onMqttMessagePublished(int messageId)
{
    QMutexLocker locker(&m_mutex);

    // 从待确认消息中移除
    if (m_pendingMessages.contains(messageId)) {
        MqttMessage message = m_pendingMessages.take(messageId);
        qInfo() << "MQTT消息发布成功 - 主题:" << message.topic << "消息ID:" << messageId;
        emit messagePublished(message.topic, messageId);

        // 重置重试计数
        m_topicRetryCount.remove(message.topic);
    }
}

void MqttManager::onMqttSubscribed(const QString& topic)
{
    qInfo() << "MQTT订阅成功:" << topic;
    emit subscribed(topic);
}

void MqttManager::onMqttUnsubscribed(const QString& topic)
{
    qInfo() << "MQTT取消订阅成功:" << topic;
    emit unsubscribed(topic);
}

void MqttManager::onMqttError(const QString& error)
{
    setLastError(QString("MQTT错误: %1").arg(error));
    qCritical() << m_lastError;
    emit errorOccurred(m_lastError);
}

void MqttManager::processMessageQueue()
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected() || m_messageQueue.isEmpty()) {
        return;
    }

    // 处理队列中的消息
    while (!m_messageQueue.isEmpty() && isConnected()) {
        MqttMessage message = m_messageQueue.dequeue();

        bool success = m_mqttClient->publish(message.topic, message.payload,
                                           message.qos, message.retain);
        if (success) {
            qDebug() << "队列消息发送成功:" << message.topic;
        } else {
            // 发送失败，重新加入队列或重试
            if (m_retryEnabled) {
                retryMessage(message);
            } else {
                qWarning() << "队列消息发送失败:" << message.topic;
            }
            break; // 停止处理队列，等待下次
        }
    }
}

void MqttManager::onRetryTimeout()
{
    // 重试定时器超时，继续处理队列
    processMessageQueue();
}

void MqttManager::setLastError(const QString& error)
{
    m_lastError = error;
}

void MqttManager::enqueueMessage(const MqttMessage& message)
{
    if (m_messageQueue.size() >= m_maxQueueSize) {
        qWarning() << "消息队列已满，丢弃最旧的消息";
        m_messageQueue.dequeue();
    }

    m_messageQueue.enqueue(message);
    qDebug() << "消息加入队列:" << message.topic << "队列大小:" << m_messageQueue.size();
}

void MqttManager::retryMessage(const MqttMessage& message)
{
    int retryCount = m_topicRetryCount.value(message.topic, 0);

    if (retryCount < m_maxRetries) {
        m_topicRetryCount[message.topic] = retryCount + 1;

        // 重新加入队列
        enqueueMessage(message);

        // 启动重试定时器
        if (!m_retryTimer->isActive()) {
            m_retryTimer->start(m_retryInterval);
        }

        qDebug() << "消息重试:" << message.topic << "重试次数:" << (retryCount + 1);
    } else {
        qWarning() << "消息重试次数超限，丢弃消息:" << message.topic;
        m_topicRetryCount.remove(message.topic);
    }
}

void MqttManager::clearMessageQueue()
{
    m_messageQueue.clear();
    m_pendingMessages.clear();
    m_topicRetryCount.clear();
    qDebug() << "消息队列已清空";
}

#include "mqttmanager.moc"
{"serial": {"portName": "COM1", "baudRate": 115200, "dataBits": 8, "parity": 0, "stopBits": 1, "flowControl": 0, "readTimeout": 3000, "writeTimeout": 3000}, "log": {"logLevel": "INFO", "logFilePath": "logs/app.log", "maxFileSize": 10, "maxFileCount": 5, "enableConsoleOutput": true}, "mqtt": {"host": "localhost", "port": 1883, "clientId": "SerialMqttBridge", "username": "", "password": "", "keepAlive": 60, "cleanSession": true}}
# 串口MQTT桥接程序 (SerialMqttBridge)

## 项目概述

本项目是一个基于Qt5的串口MQTT桥接程序，实现MCU设备通过串口AT指令与MQTT服务器的通信桥接。

## 功能特性

- ✅ 支持串口设备读写，串口设备通讯业务协议为AT指令
- ✅ 支持AT协议帧解析和封装，能够解析来自串口的AT指令和通过串口发送AT指令
- 🚧 支持MQTT服务：提供订阅、发布功能
- 🚧 根据来自串口的AT指令完成MQTT连接、订阅、发布操作
- 🚧 接收来自MQTT订阅的消息处理，并以AT指令发送到串口
- ✅ JSON格式配置文件，支持串口配置和后续扩展

## 技术栈

- **C++11** - 编程语言
- **Qt 5.15.13** - 应用框架
- **QMQTT** - MQTT客户端库
- **QSerialPort** - 串口通信

## 项目结构

```
SerialMqttBridge/
├── SerialMqttBridge.pro          # 主项目文件
├── README.md                     # 项目说明
├── src/
│   ├── main.cpp                  # 程序入口
│   ├── config/                   # ✅ 配置管理模块
│   │   ├── config.pri
│   │   ├── configmanager.h/cpp
│   │   └── appconfig.h
│   ├── protocol/                 # ✅ AT协议解析模块
│   │   ├── protocol.pri
│   │   ├── atcommandparser.h/cpp
│   │   ├── atcommand.h/cpp
│   │   └── atresponse.h/cpp
│   ├── common/                   # ✅ 公共组件
│   │   ├── common.pri
│   │   └── constants.h
│   ├── serial/                   # 🚧 串口通信模块
│   ├── mqtt/                     # 🚧 MQTT客户端模块
│   └── bridge/                   # 🚧 桥接控制器
├── config/
│   └── config.json               # 配置文件
├── doc/
│   ├── 架构设计.md                # 架构设计文档
│   └── MCU与MQTT模块交互协议说明_V1.md
└── lib/                          # 第三方库
```

## 编译说明

### 环境要求

- Qt 5.15.13 或更高版本
- C++11 支持的编译器
- QMQTT库
- 支持的平台：Windows、Linux

### 编译步骤

1. 使用Qt Creator打开 `SerialMqttBridge.pro`
2. 配置编译套件
3. 构建项目

或使用命令行：

```bash
qmake SerialMqttBridge.pro
make
```

## 配置说明

程序使用JSON格式的配置文件 `config/config.json`：

```json
{
    "serial": {
        "portName": "COM1",
        "baudRate": 115200,
        "dataBits": 8,
        "parity": 0,
        "stopBits": 1,
        "flowControl": 0,
        "readTimeout": 3000,
        "writeTimeout": 3000
    },
    "log": {
        "logLevel": "INFO",
        "logFilePath": "logs/app.log",
        "maxFileSize": 10,
        "maxFileCount": 5,
        "enableConsoleOutput": true
    },
    "mqtt": {
        "host": "localhost",
        "port": 1883,
        "clientId": "SerialMqttBridge",
        "username": "",
        "password": "",
        "keepAlive": 60,
        "cleanSession": true
    }
}
```

## 支持的AT指令

基于协议文档 `MCU与MQTT模块交互协议说明_V1.md`，支持以下AT指令：

- `AT` - 基础测试指令
- `AT+QMTOPEN` - 打开网络连接
- `AT+QMTCLOSE` - 关闭网络连接
- `AT+QMTCONN` - 连接MQTT服务器
- `AT+QMTDISC` - 断开MQTT连接
- `AT+QMTSUB` - 订阅主题
- `AT+QMTUNSUB` - 取消订阅
- `AT+QMTPUB` - 发布消息
- `AT+QMTPUBEX` - 发布消息(扩展)
- `AT+QMTCFG` - 配置MQTT参数

## 开发状态

- ✅ **已完成**：架构设计、配置管理、AT协议解析
- 🚧 **进行中**：串口通信、MQTT客户端、桥接控制器
- ⏳ **待开始**：完整功能测试、文档完善

## 设计特点

- **模块化架构** - 分层设计，职责清晰
- **SOLID原则** - 遵循面向对象设计原则
- **接口抽象** - 便于测试和扩展
- **线程安全** - 关键组件支持多线程
- **错误处理** - 完善的错误处理机制

## 许可证

本项目采用 MIT 许可证。
#ifndef CONSTANTS_H
#define CONSTANTS_H

#include <QString>

namespace Constants {

// 应用程序信息
const QString APP_NAME = "SerialMqttBridge";
const QString APP_VERSION = "1.0.0";
const QString APP_DESCRIPTION = "串口MQTT桥接程序";

// 默认配置
namespace Default {
    // 串口配置
    const QString SERIAL_PORT = "COM1";
    const int SERIAL_BAUDRATE = 115200;
    const int SERIAL_TIMEOUT = 3000;

    // MQTT配置
    const QString MQTT_HOST = "localhost";
    const int MQTT_PORT = 1883;
    const QString MQTT_CLIENT_ID = "SerialMqttBridge";
    const int MQTT_KEEP_ALIVE = 60;

    // 日志配置
    const QString LOG_LEVEL = "INFO";
    const QString LOG_FILE_PATH = "logs/app.log";
    const int LOG_MAX_FILE_SIZE = 10; // MB
    const int LOG_MAX_FILE_COUNT = 5;
}

// AT指令相关常量
namespace AtCommand {
    const QString LINE_ENDING = "\r\n";
    const int RESPONSE_TIMEOUT = 5000; // ms
    const int MAX_RETRY_COUNT = 3;
    const int RETRY_DELAY = 1000; // ms
}

// MQTT相关常量
namespace Mqtt {
    const int DEFAULT_QOS = 1;
    const bool DEFAULT_RETAIN = false;
    const bool DEFAULT_CLEAN_SESSION = true;
    const int CONNECTION_TIMEOUT = 30000; // ms
    const int RECONNECT_DELAY = 5000; // ms
    const int MAX_RECONNECT_ATTEMPTS = 10;
}

// 错误码定义
namespace ErrorCode {
    const int SUCCESS = 0;
    const int GENERAL_ERROR = -1;
    const int CONFIG_ERROR = -2;
    const int SERIAL_ERROR = -3;
    const int MQTT_ERROR = -4;
    const int PROTOCOL_ERROR = -5;
    const int TIMEOUT_ERROR = -6;
}

// 状态定义
namespace State {
    enum class ConnectionState {
        Disconnected = 0,
        Connecting,
        Connected,
        Disconnecting,
        Error
    };

    enum class BridgeState {
        Stopped = 0,
        Starting,
        Running,
        Stopping,
        Error
    };
}

} // namespace Constants

#endif // CONSTANTS_H
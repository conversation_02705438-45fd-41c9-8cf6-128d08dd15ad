#ifndef BRIDGECONTROLLER_H
#define BRIDGECONTROLLER_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QMap>
#include <QQueue>
#include <QMutex>
#include <QStateMachine>
#include <QState>
#include "appconfig.h"
#include "serialmanager.h"
#include "mqttmanager.h"
#include "atcommand.h"
#include "atresponse.h"
#include "constants.h"

/**
 * @brief 桥接状态枚举
 */
enum class BridgeState {
    Stopped = 0,        // 已停止
    Starting,           // 启动中
    Running,            // 运行中
    Stopping,           // 停止中
    Error               // 错误状态
};

/**
 * @brief AT指令处理状态枚举
 */
enum class AtCommandState {
    Idle = 0,           // 空闲
    Processing,         // 处理中
    WaitingResponse,    // 等待响应
    Completed,          // 已完成
    Failed              // 失败
};

/**
 * @brief AT指令上下文结构体
 */
struct AtCommandContext {
    AtCommand command;              // AT指令
    QString clientId;               // 客户端ID (用于MQTT操作)
    int messageId;                  // 消息ID
    QDateTime timestamp;            // 时间戳
    int retryCount;                 // 重试次数
    AtCommandState state;           // 处理状态

    AtCommandContext()
        : messageId(-1)
        , timestamp(QDateTime::currentDateTime())
        , retryCount(0)
        , state(AtCommandState::Idle)
    {}
};

/**
 * @brief 桥接控制器类
 *
 * 负责协调串口管理器和MQTT管理器，实现AT指令与MQTT操作的桥接
 * 根据协议文档实现MCU与MQTT模块的交互逻辑
 */
class BridgeController : public QObject
{
    Q_OBJECT

public:
    explicit BridgeController(QObject *parent = nullptr);
    ~BridgeController();

    /**
     * @brief 初始化桥接控制器
     * @param serialMgr 串口管理器
     * @param mqttMgr MQTT管理器
     * @return 初始化成功返回true
     */
    bool initialize(SerialManager* serialMgr, MqttManager* mqttMgr);

    /**
     * @brief 启动桥接服务
     * @return 启动成功返回true
     */
    bool start();

    /**
     * @brief 停止桥接服务
     */
    void stop();

    /**
     * @brief 获取桥接状态
     * @return 桥接状态
     */
    BridgeState getBridgeState() const;

    /**
     * @brief 检查是否正在运行
     * @return 正在运行返回true
     */
    bool isRunning() const;

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString lastError() const;

    /**
     * @brief 设置AT指令超时时间
     * @param timeout 超时时间(毫秒)
     */
    void setAtCommandTimeout(int timeout);

    /**
     * @brief 设置AT指令重试次数
     * @param maxRetries 最大重试次数
     */
    void setAtCommandMaxRetries(int maxRetries);

    /**
     * @brief 获取待处理AT指令数量
     * @return 指令数量
     */
    int getPendingCommandCount() const;

signals:
    /**
     * @brief 桥接状态变化信号
     * @param state 桥接状态
     */
    void bridgeStateChanged(BridgeState state);

    /**
     * @brief AT指令处理完成信号
     * @param command AT指令
     * @param success 是否成功
     */
    void atCommandProcessed(const AtCommand& command, bool success);

    /**
     * @brief MQTT消息转发信号
     * @param topic 主题
     * @param payload 负载
     */
    void mqttMessageForwarded(const QString& topic, const QByteArray& payload);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);

private slots:
    /**
     * @brief 处理串口连接状态变化
     * @param connected 是否连接
     */
    void onSerialConnectionChanged(bool connected);

    /**
     * @brief 处理AT指令接收
     * @param command AT指令
     */
    void onAtCommandReceived(const AtCommand& command);

    /**
     * @brief 处理AT响应接收
     * @param response AT响应
     */
    void onAtResponseReceived(const AtResponse& response);

    /**
     * @brief 处理URC接收
     * @param response URC响应
     */
    void onUrcReceived(const AtResponse& response);

    /**
     * @brief 处理串口错误
     * @param error 错误信息
     */
    void onSerialError(const QString& error);

    /**
     * @brief 处理MQTT连接状态变化
     * @param state 连接状态
     */
    void onMqttConnectionStateChanged(MqttConnectionState state);

    /**
     * @brief 处理MQTT消息接收
     * @param topic 主题
     * @param payload 负载
     */
    void onMqttMessageReceived(const QString& topic, const QByteArray& payload);

    /**
     * @brief 处理MQTT消息发布成功
     * @param topic 主题
     * @param messageId 消息ID
     */
    void onMqttMessagePublished(const QString& topic, int messageId);

    /**
     * @brief 处理MQTT订阅成功
     * @param topic 主题
     */
    void onMqttSubscribed(const QString& topic);

    /**
     * @brief 处理MQTT取消订阅成功
     * @param topic 主题
     */
    void onMqttUnsubscribed(const QString& topic);

    /**
     * @brief 处理MQTT错误
     * @param error 错误信息
     */
    void onMqttError(const QString& error);

    /**
     * @brief 处理AT指令超时
     */
    void onAtCommandTimeout();

private:
    SerialManager* m_serialManager;     // 串口管理器
    MqttManager* m_mqttManager;         // MQTT管理器
    BridgeState m_bridgeState;          // 桥接状态
    QString m_lastError;                // 最后的错误信息
    bool m_isInitialized;               // 是否已初始化

    // AT指令处理
    QQueue<AtCommandContext> m_commandQueue;    // AT指令队列
    QMap<int, AtCommandContext> m_pendingCommands; // 待处理指令
    QTimer* m_commandTimer;             // 指令超时定时器
    int m_atCommandTimeout;             // AT指令超时时间
    int m_maxRetries;                   // 最大重试次数
    int m_nextMessageId;                // 下一个消息ID

    // MQTT客户端状态跟踪
    QMap<int, MqttConnectionState> m_clientStates; // 客户端状态
    QMap<int, QString> m_clientHosts;   // 客户端主机
    QMap<int, quint16> m_clientPorts;   // 客户端端口

    mutable QMutex m_mutex;             // 线程安全锁

    /**
     * @brief 设置桥接状态
     * @param state 桥接状态
     */
    void setBridgeState(BridgeState state);

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setLastError(const QString& error);

    /**
     * @brief 处理AT指令
     * @param command AT指令
     */
    void processAtCommand(const AtCommand& command);

    /**
     * @brief 发送AT响应
     * @param response AT响应
     */
    void sendAtResponse(const AtResponse& response);

    /**
     * @brief 发送URC
     * @param urcType URC类型
     * @param parameters 参数列表
     */
    void sendUrc(UrcType urcType, const QStringList& parameters = QStringList());

    /**
     * @brief 生成下一个消息ID
     * @return 消息ID
     */
    int generateMessageId();

    /**
     * @brief 获取客户端索引
     * @param clientId 客户端ID
     * @return 客户端索引
     */
    int getClientIndex(const QString& clientId = QString()) const;

    // AT指令处理方法
    bool processQmtOpen(const AtCommand& command, AtCommandContext& context);
    bool processQmtClose(const AtCommand& command, AtCommandContext& context);
    bool processQmtConn(const AtCommand& command, AtCommandContext& context);
    bool processQmtDisc(const AtCommand& command, AtCommandContext& context);
    bool processQmtSub(const AtCommand& command, AtCommandContext& context);
    bool processQmtUnsub(const AtCommand& command, AtCommandContext& context);
    bool processQmtPub(const AtCommand& command, AtCommandContext& context);
    bool processQmtPubEx(const AtCommand& command, AtCommandContext& context);
    bool processQmtCfg(const AtCommand& command, AtCommandContext& context);
};

#endif // BRIDGECONTROLLER_H
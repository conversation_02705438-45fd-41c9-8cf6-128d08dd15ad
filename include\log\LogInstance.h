/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* LogInstance.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年12月05日
* 摘要：日志文件的输入接口
*     提供基于<<的服务
* 当前版本：1.0
*/
#ifndef LOGINSTANCE_H
#define LOGINSTANCE_H
#include <QtCore>
#include "LogInfo.h"
#include "logger_global.h"

class LoggerPrivate;
class LOGGERSHARED_EXPORT LogInstance : public QDebug
{
public:
    /************************************************
     * 功能：构造函数
     * 输入参数：
     *      level -- 日志等级
     *      pLogger -- 对应的日志模块
     ************************************************/
    LogInstance( Logging::LogLevel level, LoggerPrivate* pLogger );

    /************************************************
     * 功能：析构函数
     ************************************************/
    ~LogInstance();
private:
    LoggerPrivate* m_pLogger;//日志模块
    Logging::LogLevel m_level;//等级
    QString m_strBuffer;//缓存
};

#endif // LOGINSTANCE_H

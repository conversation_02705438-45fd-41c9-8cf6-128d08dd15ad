#include "mqttclient.h"
#include <QMqttClient>
#include <QMqttMessage>
#include <QMqttSubscription>
#include <QMutexLocker>
#include <QDebug>

MqttClient::MqttClient(QObject *parent)
    : QObject(parent)
    , m_client(nullptr)
    , m_state(MqttConnectionState::Disconnected)
    , m_reconnectTimer(nullptr)
    , m_autoReconnect(false)
    , m_reconnectInterval(5000)
    , m_connectionTimeout(30000)
{
    m_client = new QMqttClient(this);
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);

    // 连接MQTT客户端信号
    connect(m_client, &QMqttClient::stateChanged,
            this, &MqttClient::onConnectionStateChanged);
    connect(m_client, &QMqttClient::messageReceived,
            this, &MqttClient::onMessageReceived);
    connect(m_client, &QMqttClient::errorChanged,
            this, &MqttClient::onErrorOccurred);

    // 连接重连定时器
    connect(m_reconnectTimer, &QTimer::timeout,
            this, &MqttClient::onReconnectTimeout);
}

MqttClient::~MqttClient()
{
    disconnectFromHost();
    cleanupSubscriptions();
}

bool MqttClient::connectToHost(const QString& host, quint16 port)
{
    QMutexLocker locker(&m_mutex);

    if (m_state == MqttConnectionState::Connected) {
        return true; // 已经连接
    }

    if (m_state == MqttConnectionState::Connecting) {
        return false; // 正在连接中
    }

    // 设置连接参数
    m_client->setHostname(host);
    m_client->setPort(port);

    if (!m_config.clientId.isEmpty()) {
        m_client->setClientId(m_config.clientId);
    }

    if (!m_config.username.isEmpty()) {
        m_client->setUsername(m_config.username);
    }

    if (!m_config.password.isEmpty()) {
        m_client->setPassword(m_config.password);
    }

    m_client->setKeepAlive(m_config.keepAlive);
    m_client->setCleanSession(m_config.cleanSession);

    qInfo() << "连接MQTT服务器:" << host << ":" << port
            << "客户端ID:" << m_config.clientId;

    // 开始连接
    m_client->connectToHost();

    return true;
}

void MqttClient::disconnectFromHost()
{
    QMutexLocker locker(&m_mutex);

    if (m_reconnectTimer && m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }

    if (m_client && m_state != MqttConnectionState::Disconnected) {
        qInfo() << "断开MQTT连接";
        m_client->disconnectFromHost();
    }

    cleanupSubscriptions();
}

bool MqttClient::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_state == MqttConnectionState::Connected;
}

bool MqttClient::subscribe(const QString& topic, MqttQoS qos)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("MQTT未连接");
        return false;
    }

    if (topic.isEmpty()) {
        setLastError("主题名称不能为空");
        return false;
    }

    // 检查是否已经订阅
    if (m_subscriptions.contains(topic)) {
        qWarning() << "主题已经订阅:" << topic;
        return true;
    }

    // 创建订阅
    QMqttSubscription* subscription = m_client->subscribe(topic, convertQoS(qos));
    if (!subscription) {
        setLastError(QString("订阅主题失败: %1").arg(topic));
        return false;
    }

    // 连接订阅信号
    connect(subscription, &QMqttSubscription::messageReceived,
            this, &MqttClient::onMessageReceived);

    m_subscriptions[topic] = subscription;

    qInfo() << "订阅主题:" << topic << "QoS:" << static_cast<int>(qos);
    emit subscribed(topic);

    return true;
}

bool MqttClient::unsubscribe(const QString& topic)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("MQTT未连接");
        return false;
    }

    if (!m_subscriptions.contains(topic)) {
        qWarning() << "主题未订阅:" << topic;
        return true;
    }

    // 取消订阅
    m_client->unsubscribe(topic);

    // 清理订阅对象
    QMqttSubscription* subscription = m_subscriptions.take(topic);
    if (subscription) {
        subscription->deleteLater();
    }

    qInfo() << "取消订阅主题:" << topic;
    emit unsubscribed(topic);

    return true;
}

bool MqttClient::publish(const QString& topic, const QByteArray& payload,
                        MqttQoS qos, bool retain)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("MQTT未连接");
        return false;
    }

    if (topic.isEmpty()) {
        setLastError("主题名称不能为空");
        return false;
    }

    // 发布消息
    qint32 messageId = m_client->publish(topic, payload, convertQoS(qos), retain);
    if (messageId == -1) {
        setLastError(QString("发布消息失败: %1").arg(topic));
        return false;
    }

    qInfo() << "发布消息到主题:" << topic
            << "QoS:" << static_cast<int>(qos)
            << "保留:" << retain
            << "消息ID:" << messageId
            << "负载大小:" << payload.size();

    emit messagePublished(messageId);

    return true;
}

MqttConnectionState MqttClient::getConnectionState() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

QString MqttClient::lastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void MqttClient::setConfiguration(const MqttConfig& config)
{
    QMutexLocker locker(&m_mutex);
    m_config = config;

    qInfo() << "MQTT配置已设置:"
            << "主机:" << config.host
            << "端口:" << config.port
            << "客户端ID:" << config.clientId
            << "心跳:" << config.keepAlive
            << "清理会话:" << config.cleanSession;
}

void MqttClient::setConnectionTimeout(int timeout)
{
    QMutexLocker locker(&m_mutex);
    m_connectionTimeout = timeout;
    qDebug() << "MQTT连接超时设置:" << timeout << "ms";
}

void MqttClient::setKeepAlive(int keepAlive)
{
    QMutexLocker locker(&m_mutex);
    m_config.keepAlive = keepAlive;
    if (m_client) {
        m_client->setKeepAlive(keepAlive);
    }
    qDebug() << "MQTT心跳间隔设置:" << keepAlive << "秒";
}

void MqttClient::setAutoReconnect(bool enabled, int interval)
{
    QMutexLocker locker(&m_mutex);
    m_autoReconnect = enabled;
    m_reconnectInterval = interval;

    qInfo() << "MQTT自动重连设置:" << (enabled ? "启用" : "禁用")
            << "间隔:" << interval << "ms";
}

QStringList MqttClient::getSubscribedTopics() const
{
    QMutexLocker locker(&m_mutex);
    return m_subscriptions.keys();
}

void MqttClient::onConnectionStateChanged()
{
    QMqttClient::ClientState state = m_client->state();
    MqttConnectionState newState = convertConnectionState(static_cast<int>(state));

    if (m_state != newState) {
        m_state = newState;

        QString stateStr;
        switch (newState) {
        case MqttConnectionState::Disconnected:
            stateStr = "已断开";
            emit disconnected();
            if (m_autoReconnect) {
                m_reconnectTimer->start(m_reconnectInterval);
            }
            break;
        case MqttConnectionState::Connecting:
            stateStr = "连接中";
            break;
        case MqttConnectionState::Connected:
            stateStr = "已连接";
            emit connected();
            break;
        case MqttConnectionState::Disconnecting:
            stateStr = "断开中";
            break;
        case MqttConnectionState::Error:
            stateStr = "错误";
            break;
        }

        qInfo() << "MQTT连接状态变化:" << stateStr;
        emit connectionStateChanged(newState);
    }
}

void MqttClient::onMessageReceived(const QMqttMessage& message)
{
    QString topic = message.topic().name();
    QByteArray payload = message.payload();

    qInfo() << "收到MQTT消息 - 主题:" << topic
            << "负载大小:" << payload.size()
            << "QoS:" << message.qos()
            << "保留:" << message.retain();

    emit messageReceived(topic, payload);
}

void MqttClient::onErrorOccurred()
{
    QMqttClient::ClientError error = m_client->error();
    QString errorString;

    switch (error) {
    case QMqttClient::NoError:
        return; // 没有错误
    case QMqttClient::InvalidProtocolVersion:
        errorString = "无效的协议版本";
        break;
    case QMqttClient::IdRejected:
        errorString = "客户端ID被拒绝";
        break;
    case QMqttClient::ServerUnavailable:
        errorString = "服务器不可用";
        break;
    case QMqttClient::BadUsernameOrPassword:
        errorString = "用户名或密码错误";
        break;
    case QMqttClient::NotAuthorized:
        errorString = "未授权";
        break;
    case QMqttClient::TransportInvalid:
        errorString = "传输无效";
        break;
    case QMqttClient::ProtocolViolation:
        errorString = "协议违规";
        break;
    case QMqttClient::UnknownError:
    default:
        errorString = "未知错误";
        break;
    }

    QString fullError = QString("MQTT错误 (%1): %2")
                       .arg(static_cast<int>(error))
                       .arg(errorString);

    setLastError(fullError);
    qCritical() << fullError;

    m_state = MqttConnectionState::Error;
    emit errorOccurred(fullError);
    emit connectionStateChanged(m_state);
}

void MqttClient::onReconnectTimeout()
{
    if (m_autoReconnect && m_state == MqttConnectionState::Disconnected) {
        qInfo() << "尝试MQTT自动重连...";
        attemptReconnect();
    }
}

void MqttClient::setLastError(const QString& error)
{
    m_lastError = error;
}

void MqttClient::attemptReconnect()
{
    if (!m_autoReconnect) {
        return;
    }

    if (m_state == MqttConnectionState::Connected ||
        m_state == MqttConnectionState::Connecting) {
        return; // 已经连接或正在连接
    }

    qInfo() << "尝试重连MQTT服务器:" << m_config.host << ":" << m_config.port;

    if (connectToHost(m_config.host, m_config.port)) {
        qInfo() << "MQTT重连请求已发送";
    } else {
        qWarning() << "MQTT重连失败，将继续重试";
        // 继续重连
        m_reconnectTimer->start(m_reconnectInterval);
    }
}

void MqttClient::cleanupSubscriptions()
{
    for (auto it = m_subscriptions.begin(); it != m_subscriptions.end(); ++it) {
        if (it.value()) {
            it.value()->deleteLater();
        }
    }
    m_subscriptions.clear();
}

quint8 MqttClient::convertQoS(MqttQoS qos) const
{
    switch (qos) {
    case MqttQoS::AtMostOnce:
        return 0;
    case MqttQoS::AtLeastOnce:
        return 1;
    case MqttQoS::ExactlyOnce:
        return 2;
    default:
        return 1; // 默认QoS 1
    }
}

MqttConnectionState MqttClient::convertConnectionState(int state) const
{
    switch (state) {
    case QMqttClient::Disconnected:
        return MqttConnectionState::Disconnected;
    case QMqttClient::Connecting:
        return MqttConnectionState::Connecting;
    case QMqttClient::Connected:
        return MqttConnectionState::Connected;
    default:
        return MqttConnectionState::Error;
    }
}

#include "mqttclient.moc"
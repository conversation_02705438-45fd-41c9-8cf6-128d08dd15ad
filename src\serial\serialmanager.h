#ifndef SERIALMANAGER_H
#define SERIALMANAGER_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QQueue>
#include <QMutex>
#include "serialdevice.h"
#include "atcommandparser.h"
#include "appconfig.h"

/**
 * @brief 串口管理器类
 *
 * 负责串口设备的管理和AT指令的收发处理
 * 集成AT协议解析器，提供高级的AT指令接口
 */
class SerialManager : public QObject
{
    Q_OBJECT

public:
    explicit SerialManager(QObject *parent = nullptr);
    ~SerialManager();

    /**
     * @brief 初始化串口管理器
     * @param config 串口配置
     * @return 初始化成功返回true
     */
    bool initialize(const SerialConfig& config);

    /**
     * @brief 启动串口管理器
     * @return 启动成功返回true
     */
    bool start();

    /**
     * @brief 停止串口管理器
     */
    void stop();

    /**
     * @brief 检查是否已连接
     * @return 已连接返回true
     */
    bool isConnected() const;

    /**
     * @brief 发送AT指令
     * @param command AT指令对象
     * @return 发送成功返回true
     */
    bool sendAtCommand(const AtCommand& command);

    /**
     * @brief 发送AT指令字符串
     * @param commandStr AT指令字符串
     * @return 发送成功返回true
     */
    bool sendAtCommand(const QString& commandStr);

    /**
     * @brief 发送原始数据
     * @param data 原始数据
     * @return 发送成功返回true
     */
    bool sendRawData(const QByteArray& data);

    /**
     * @brief 获取串口设备信息
     * @return 串口设备指针
     */
    ISerialDevice* getSerialDevice() const;

    /**
     * @brief 获取AT指令解析器
     * @return AT指令解析器指针
     */
    AtCommandParser* getAtParser() const;

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString lastError() const;

    /**
     * @brief 设置自动重连
     * @param enabled 是否启用自动重连
     * @param interval 重连间隔(毫秒)
     */
    void setAutoReconnect(bool enabled, int interval = 5000);

    /**
     * @brief 获取可用串口列表
     * @return 串口名称列表
     */
    static QStringList getAvailablePorts();

signals:
    /**
     * @brief 连接状态变化信号
     * @param connected 是否连接
     */
    void connectionChanged(bool connected);

    /**
     * @brief AT指令发送信号
     * @param command AT指令对象
     */
    void atCommandSent(const AtCommand& command);

    /**
     * @brief AT响应接收信号
     * @param response AT响应对象
     */
    void atResponseReceived(const AtResponse& response);

    /**
     * @brief URC接收信号
     * @param response URC响应对象
     */
    void urcReceived(const AtResponse& response);

    /**
     * @brief 原始数据接收信号
     * @param data 原始数据
     */
    void rawDataReceived(const QByteArray& data);

    /**
     * @brief 原始数据发送信号
     * @param data 原始数据
     */
    void rawDataSent(const QByteArray& data);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);

private slots:
    /**
     * @brief 处理串口数据接收
     * @param data 接收到的数据
     */
    void onSerialDataReceived(const QByteArray& data);

    /**
     * @brief 处理串口连接状态变化
     * @param connected 是否连接
     */
    void onSerialConnectionChanged(bool connected);

    /**
     * @brief 处理串口错误
     * @param error 错误信息
     */
    void onSerialError(const QString& error);

    /**
     * @brief 处理AT指令解析完成
     * @param command AT指令对象
     */
    void onAtCommandParsed(const AtCommand& command);

    /**
     * @brief 处理AT响应解析完成
     * @param response AT响应对象
     */
    void onAtResponseParsed(const AtResponse& response);

    /**
     * @brief 处理URC接收
     * @param response URC响应对象
     */
    void onUrcReceived(const AtResponse& response);

    /**
     * @brief 处理AT解析错误
     * @param error 错误信息
     */
    void onAtParseError(const QString& error);

    /**
     * @brief 自动重连定时器超时
     */
    void onReconnectTimeout();

private:
    SerialDevice* m_serialDevice;       // 串口设备
    AtCommandParser* m_atParser;        // AT指令解析器
    QTimer* m_reconnectTimer;           // 重连定时器

    SerialConfig m_config;              // 串口配置
    QString m_lastError;                // 最后的错误信息
    bool m_autoReconnect;               // 是否自动重连
    int m_reconnectInterval;            // 重连间隔
    bool m_isInitialized;               // 是否已初始化

    mutable QMutex m_mutex;             // 线程安全锁

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setLastError(const QString& error);

    /**
     * @brief 尝试重连
     */
    void attemptReconnect();
};

#endif // SERIALMANAGER_H
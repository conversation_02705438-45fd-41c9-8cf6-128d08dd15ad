#include "serialdevice.h"
#include <QSerialPortInfo>
#include <QDebug>

SerialDevice::SerialDevice(QObject *parent)
    : QObject(parent)
    , m_serialPort(nullptr)
    , m_writeTimer(nullptr)
    , m_readTimeout(3000)
    , m_writeTimeout(3000)
{
    m_serialPort = new QSerialPort(this);
    m_writeTimer = new QTimer(this);
    m_writeTimer->setSingleShot(true);

    // 连接信号
    connect(m_serialPort, &QSerialPort::readyRead, this, &SerialDevice::onDataReceived);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::errorOccurred),
            this, &SerialDevice::onSerialError);
    connect(m_writeTimer, &QTimer::timeout, this, &SerialDevice::onWriteTimeout);
}

SerialDevice::~SerialDevice()
{
    close();
}

bool SerialDevice::open()
{
    if (m_serialPort->isOpen()) {
        return true;
    }

    if (m_serialPort->portName().isEmpty()) {
        setLastError("串口名称未设置");
        return false;
    }

    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        setLastError(QString("无法打开串口 %1: %2")
                    .arg(m_serialPort->portName())
                    .arg(m_serialPort->errorString()));
        return false;
    }

    // 清空缓冲区
    clear();

    qInfo() << "串口已打开:" << m_serialPort->portName()
            << "波特率:" << m_serialPort->baudRate()
            << "数据位:" << m_serialPort->dataBits()
            << "校验位:" << m_serialPort->parity()
            << "停止位:" << m_serialPort->stopBits()
            << "流控制:" << m_serialPort->flowControl();

    emit connectionChanged(true);
    return true;
}

void SerialDevice::close()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
        qInfo() << "串口已关闭:" << m_serialPort->portName();
        emit connectionChanged(false);
    }

    if (m_writeTimer && m_writeTimer->isActive()) {
        m_writeTimer->stop();
    }
}

bool SerialDevice::isOpen() const
{
    return m_serialPort && m_serialPort->isOpen();
}

qint64 SerialDevice::write(const QByteArray& data)
{
    if (!isOpen()) {
        setLastError("串口未打开");
        return -1;
    }

    if (data.isEmpty()) {
        return 0;
    }

    // 启动写超时定时器
    if (m_writeTimeout > 0) {
        m_writeTimer->start(m_writeTimeout);
    }

    qint64 bytesWritten = m_serialPort->write(data);

    if (bytesWritten == -1) {
        setLastError(QString("写入数据失败: %1").arg(m_serialPort->errorString()));
        m_writeTimer->stop();
        return -1;
    }

    // 等待数据写入完成
    if (!m_serialPort->waitForBytesWritten(m_writeTimeout)) {
        setLastError("写入数据超时");
        m_writeTimer->stop();
        return -1;
    }

    m_writeTimer->stop();

    qDebug() << "串口发送数据:" << data.toHex(' ') << "字节数:" << bytesWritten;
    emit dataSent(bytesWritten);

    return bytesWritten;
}

QByteArray SerialDevice::readAll()
{
    if (!isOpen()) {
        setLastError("串口未打开");
        return QByteArray();
    }

    QByteArray data = m_serialPort->readAll();
    if (!data.isEmpty()) {
        qDebug() << "串口接收数据:" << data.toHex(' ') << "字节数:" << data.size();
    }

    return data;
}

QString SerialDevice::portName() const
{
    return m_serialPort ? m_serialPort->portName() : QString();
}

bool SerialDevice::setPortSettings(const QString& portName,
                                  QSerialPort::BaudRate baudRate,
                                  QSerialPort::DataBits dataBits,
                                  QSerialPort::Parity parity,
                                  QSerialPort::StopBits stopBits,
                                  QSerialPort::FlowControl flowControl)
{
    if (isOpen()) {
        setLastError("无法在串口打开时修改设置");
        return false;
    }

    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(baudRate);
    m_serialPort->setDataBits(dataBits);
    m_serialPort->setParity(parity);
    m_serialPort->setStopBits(stopBits);
    m_serialPort->setFlowControl(flowControl);

    qInfo() << "串口参数已设置:" << portName
            << "波特率:" << baudRate
            << "数据位:" << dataBits
            << "校验位:" << parity
            << "停止位:" << stopBits
            << "流控制:" << flowControl;

    return true;
}

void SerialDevice::setTimeout(int readTimeout, int writeTimeout)
{
    m_readTimeout = readTimeout;
    m_writeTimeout = writeTimeout;

    qDebug() << "串口超时设置 - 读:" << m_readTimeout << "ms, 写:" << m_writeTimeout << "ms";
}

QString SerialDevice::lastError() const
{
    return m_lastError;
}

void SerialDevice::clear()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->clear();
        qDebug() << "串口缓冲区已清空";
    }
}

QStringList SerialDevice::availablePorts()
{
    QStringList portNames;
    const auto serialPortInfos = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &portInfo : serialPortInfos) {
        portNames << portInfo.portName();
        qDebug() << "可用串口:" << portInfo.portName()
                 << "描述:" << portInfo.description()
                 << "制造商:" << portInfo.manufacturer()
                 << "系统位置:" << portInfo.systemLocation()
                 << "VID:" << QString::number(portInfo.vendorIdentifier(), 16)
                 << "PID:" << QString::number(portInfo.productIdentifier(), 16);
    }

    return portNames;
}

void SerialDevice::onDataReceived()
{
    QByteArray data = readAll();
    if (!data.isEmpty()) {
        emit dataReceived(data);
    }
}

void SerialDevice::onSerialError(QSerialPort::SerialPortError error)
{
    if (error == QSerialPort::NoError) {
        return;
    }

    QString errorString;
    switch (error) {
    case QSerialPort::DeviceNotFoundError:
        errorString = "设备未找到";
        break;
    case QSerialPort::PermissionError:
        errorString = "权限错误";
        break;
    case QSerialPort::OpenError:
        errorString = "打开错误";
        break;
    case QSerialPort::ParityError:
        errorString = "校验错误";
        break;
    case QSerialPort::FramingError:
        errorString = "帧错误";
        break;
    case QSerialPort::BreakConditionError:
        errorString = "中断条件错误";
        break;
    case QSerialPort::WriteError:
        errorString = "写入错误";
        break;
    case QSerialPort::ReadError:
        errorString = "读取错误";
        break;
    case QSerialPort::ResourceError:
        errorString = "资源错误";
        break;
    case QSerialPort::UnsupportedOperationError:
        errorString = "不支持的操作";
        break;
    case QSerialPort::TimeoutError:
        errorString = "超时错误";
        break;
    case QSerialPort::NotOpenError:
        errorString = "串口未打开";
        break;
    default:
        errorString = "未知错误";
        break;
    }

    QString fullError = QString("串口错误 (%1): %2 - %3")
                       .arg(static_cast<int>(error))
                       .arg(errorString)
                       .arg(m_serialPort->errorString());

    setLastError(fullError);
    qCritical() << fullError;

    emit errorOccurred(fullError);

    // 严重错误时自动关闭串口
    if (error == QSerialPort::ResourceError ||
        error == QSerialPort::DeviceNotFoundError ||
        error == QSerialPort::PermissionError) {
        close();
    }
}

void SerialDevice::onWriteTimeout()
{
    setLastError("写入数据超时");
    qWarning() << "串口写入超时";
    emit errorOccurred(m_lastError);
}

void SerialDevice::setLastError(const QString& error)
{
    m_lastError = error;
}

#include "serialdevice.moc"
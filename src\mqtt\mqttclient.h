#ifndef MQTTCLIENT_H
#define MQTTCLIENT_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QMap>
#include <QMutex>
#include "appconfig.h"

// 前向声明QMQTT类
class QMqttClient;
class QMqttMessage;
class QMqttSubscription;

/**
 * @brief MQTT连接状态枚举
 */
enum class MqttConnectionState {
    Disconnected = 0,
    Connecting,
    Connected,
    Disconnecting,
    Error
};

/**
 * @brief MQTT服务质量枚举
 */
enum class MqttQoS {
    AtMostOnce = 0,     // QoS 0
    AtLeastOnce = 1,    // QoS 1
    ExactlyOnce = 2     // QoS 2
};

/**
 * @brief MQTT客户端接口
 */
class IMqttClient
{
public:
    virtual ~IMqttClient() = default;

    /**
     * @brief 连接到MQTT服务器
     * @param host 服务器地址
     * @param port 服务器端口
     * @return 连接成功返回true
     */
    virtual bool connectToHost(const QString& host, quint16 port) = 0;

    /**
     * @brief 断开MQTT连接
     */
    virtual void disconnectFromHost() = 0;

    /**
     * @brief 检查是否已连接
     * @return 已连接返回true
     */
    virtual bool isConnected() const = 0;

    /**
     * @brief 订阅主题
     * @param topic 主题名称
     * @param qos 服务质量
     * @return 订阅成功返回true
     */
    virtual bool subscribe(const QString& topic, MqttQoS qos = MqttQoS::AtLeastOnce) = 0;

    /**
     * @brief 取消订阅主题
     * @param topic 主题名称
     * @return 取消订阅成功返回true
     */
    virtual bool unsubscribe(const QString& topic) = 0;

    /**
     * @brief 发布消息
     * @param topic 主题名称
     * @param payload 消息内容
     * @param qos 服务质量
     * @param retain 是否保留消息
     * @return 发布成功返回true
     */
    virtual bool publish(const QString& topic, const QByteArray& payload,
                        MqttQoS qos = MqttQoS::AtLeastOnce, bool retain = false) = 0;

    /**
     * @brief 获取连接状态
     * @return 连接状态
     */
    virtual MqttConnectionState getConnectionState() const = 0;

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    virtual QString lastError() const = 0;
};

/**
 * @brief MQTT客户端实现类
 */
class MqttClient : public QObject, public IMqttClient
{
    Q_OBJECT

public:
    explicit MqttClient(QObject *parent = nullptr);
    ~MqttClient();

    // 实现IMqttClient接口
    bool connectToHost(const QString& host, quint16 port) override;
    void disconnectFromHost() override;
    bool isConnected() const override;
    bool subscribe(const QString& topic, MqttQoS qos = MqttQoS::AtLeastOnce) override;
    bool unsubscribe(const QString& topic) override;
    bool publish(const QString& topic, const QByteArray& payload,
                MqttQoS qos = MqttQoS::AtLeastOnce, bool retain = false) override;
    MqttConnectionState getConnectionState() const override;
    QString lastError() const override;

    /**
     * @brief 设置客户端配置
     * @param config MQTT配置
     */
    void setConfiguration(const MqttConfig& config);

    /**
     * @brief 设置连接超时时间
     * @param timeout 超时时间(毫秒)
     */
    void setConnectionTimeout(int timeout);

    /**
     * @brief 设置心跳间隔
     * @param keepAlive 心跳间隔(秒)
     */
    void setKeepAlive(int keepAlive);

    /**
     * @brief 设置自动重连
     * @param enabled 是否启用自动重连
     * @param interval 重连间隔(毫秒)
     */
    void setAutoReconnect(bool enabled, int interval = 5000);

    /**
     * @brief 获取已订阅的主题列表
     * @return 主题列表
     */
    QStringList getSubscribedTopics() const;

signals:
    /**
     * @brief 连接状态变化信号
     * @param state 连接状态
     */
    void connectionStateChanged(MqttConnectionState state);

    /**
     * @brief 连接成功信号
     */
    void connected();

    /**
     * @brief 断开连接信号
     */
    void disconnected();

    /**
     * @brief 消息接收信号
     * @param topic 主题名称
     * @param payload 消息内容
     */
    void messageReceived(const QString& topic, const QByteArray& payload);

    /**
     * @brief 消息发布成功信号
     * @param messageId 消息ID
     */
    void messagePublished(int messageId);

    /**
     * @brief 订阅成功信号
     * @param topic 主题名称
     */
    void subscribed(const QString& topic);

    /**
     * @brief 取消订阅成功信号
     * @param topic 主题名称
     */
    void unsubscribed(const QString& topic);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);

private slots:
    /**
     * @brief 处理连接状态变化
     */
    void onConnectionStateChanged();

    /**
     * @brief 处理消息接收
     * @param message MQTT消息
     */
    void onMessageReceived(const QMqttMessage& message);

    /**
     * @brief 处理错误
     */
    void onErrorOccurred();

    /**
     * @brief 自动重连定时器超时
     */
    void onReconnectTimeout();

private:
    QMqttClient* m_client;              // QMQTT客户端
    MqttConfig m_config;                // MQTT配置
    MqttConnectionState m_state;        // 连接状态
    QString m_lastError;                // 最后的错误信息

    QTimer* m_reconnectTimer;           // 重连定时器
    bool m_autoReconnect;               // 是否自动重连
    int m_reconnectInterval;            // 重连间隔
    int m_connectionTimeout;            // 连接超时

    QMap<QString, QMqttSubscription*> m_subscriptions; // 订阅管理
    mutable QMutex m_mutex;             // 线程安全锁

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setLastError(const QString& error);

    /**
     * @brief 尝试重连
     */
    void attemptReconnect();

    /**
     * @brief 清理订阅
     */
    void cleanupSubscriptions();

    /**
     * @brief 转换QoS枚举
     * @param qos 自定义QoS枚举
     * @return QMQTT QoS值
     */
    quint8 convertQoS(MqttQoS qos) const;

    /**
     * @brief 转换连接状态
     * @param state QMQTT连接状态
     * @return 自定义连接状态
     */
    MqttConnectionState convertConnectionState(int state) const;
};

#endif // MQTTCLIENT_H
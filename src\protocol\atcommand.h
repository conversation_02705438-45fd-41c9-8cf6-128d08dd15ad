#ifndef ATCOMMAND_H
#define ATCOMMAND_H

#include <QString>
#include <QStringList>
#include <QVariant>
#include <QMap>

/**
 * @brief AT指令类型枚举
 */
enum class AtCommandType {
    Unknown = 0,
    Basic,              // 基础指令 (AT)
    Test,               // 测试指令 (AT+CMD=?)
    Query,              // 查询指令 (AT+CMD?)
    Set,                // 设置指令 (AT+CMD=param1,param2,...)
    Execute             // 执行指令 (AT+CMD)
};

/**
 * @brief MQTT相关AT指令枚举
 */
enum class MqttAtCommand {
    Unknown = 0,
    AT,                 // AT
    QMTOPEN,           // AT+QMTOPEN - 打开网络连接
    QMTCLOSE,          // AT+QMTCLOSE - 关闭网络连接
    QMTCONN,           // AT+QMTCONN - 连接MQTT服务器
    QMTDISC,           // AT+QMTDISC - 断开MQTT连接
    QMTSUB,            // AT+QMTSUB - 订阅主题
    QMTUNSUB,          // AT+QMTUNSUB - 取消订阅
    QMTPUB,            // AT+QMTPUB - 发布消息
    QMTPUBEX,          // AT+QMTPUBEX - 发布消息(扩展)
    QMTCFG             // AT+QMTCFG - 配置MQTT参数
};

/**
 * @brief AT指令类
 */
class AtCommand
{
public:
    AtCommand();
    AtCommand(const QString& rawCommand);
    AtCommand(MqttAtCommand command, AtCommandType type = AtCommandType::Execute);

    /**
     * @brief 解析原始AT指令字符串
     * @param rawCommand 原始指令字符串
     * @return 解析成功返回true
     */
    bool parse(const QString& rawCommand);

    /**
     * @brief 格式化为AT指令字符串
     * @return 格式化后的指令字符串
     */
    QString format() const;

    /**
     * @brief 检查指令是否有效
     * @return 有效返回true
     */
    bool isValid() const;

    // Getter方法
    MqttAtCommand getCommand() const { return m_command; }
    AtCommandType getType() const { return m_type; }
    QString getRawCommand() const { return m_rawCommand; }
    QStringList getParameters() const { return m_parameters; }
    QString getParameter(int index) const;
    int getParameterCount() const { return m_parameters.size(); }

    // Setter方法
    void setCommand(MqttAtCommand command) { m_command = command; }
    void setType(AtCommandType type) { m_type = type; }
    void setParameters(const QStringList& parameters) { m_parameters = parameters; }
    void addParameter(const QString& parameter) { m_parameters.append(parameter); }
    void clearParameters() { m_parameters.clear(); }

    /**
     * @brief 获取指令名称字符串
     * @param command MQTT AT指令枚举
     * @return 指令名称字符串
     */
    static QString getCommandString(MqttAtCommand command);

    /**
     * @brief 从字符串获取指令枚举
     * @param commandStr 指令字符串
     * @return MQTT AT指令枚举
     */
    static MqttAtCommand getCommandFromString(const QString& commandStr);

private:
    MqttAtCommand m_command;        // MQTT指令类型
    AtCommandType m_type;           // AT指令类型
    QString m_rawCommand;           // 原始指令字符串
    QStringList m_parameters;       // 参数列表

    /**
     * @brief 解析指令类型和参数
     * @param commandPart 指令部分字符串
     */
    void parseCommandAndType(const QString& commandPart);

    /**
     * @brief 解析参数列表
     * @param paramPart 参数部分字符串
     */
    void parseParameters(const QString& paramPart);

    /**
     * @brief 移除字符串两端的引号
     * @param str 输入字符串
     * @return 移除引号后的字符串
     */
    QString removeQuotes(const QString& str) const;
};

#endif // ATCOMMAND_H
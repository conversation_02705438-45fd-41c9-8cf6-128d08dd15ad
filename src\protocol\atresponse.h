#ifndef ATRESPONSE_H
#define ATRESPONSE_H

#include <QString>
#include <QStringList>
#include <QDateTime>
#include "atcommand.h"

/**
 * @brief AT响应类型枚举
 */
enum class AtResponseType {
    Unknown = 0,
    OK,                 // OK响应
    ERROR,              // ERROR响应
    URC,                // 非请求结果码 (Unsolicited Result Code)
    Data,               // 数据响应
    Prompt              // 提示符响应 (如 >)
};

/**
 * @brief URC类型枚举
 */
enum class UrcType {
    Unknown = 0,
    QMTOPEN,           // +QMTOPEN: 网络连接状态
    QMTCLOSE,          // +QMTCLOSE: 网络关闭状态
    QMTCONN,           // +QMTCONN: MQTT连接状态
    QMTDISC,           // +QMTDISC: MQTT断开状态
    QMTSUB,            // +QMTSUB: 订阅结果
    QMTUNSUB,          // +QMTUNSUB: 取消订阅结果
    QMTPUB,            // +QMTPUB: 发布结果
    QMTPUBEX,          // +QMTPUBEX: 发布结果(扩展)
    QMTRECV,           // +QMTRECV: 接收到消息
    QMTSTAT,           // +QMTSTAT: MQTT状态变化
    QMTPING            // +QMTPING: 心跳状态
};

/**
 * @brief AT响应类
 */
class AtResponse
{
public:
    AtResponse();
    AtResponse(const QString& rawResponse);
    AtResponse(AtResponseType type, const QString& content = QString());

    /**
     * @brief 解析原始响应字符串
     * @param rawResponse 原始响应字符串
     * @return 解析成功返回true
     */
    bool parse(const QString& rawResponse);

    /**
     * @brief 格式化为响应字符串
     * @return 格式化后的响应字符串
     */
    QString format() const;

    /**
     * @brief 检查响应是否有效
     * @return 有效返回true
     */
    bool isValid() const;

    /**
     * @brief 检查是否为成功响应
     * @return 成功返回true
     */
    bool isSuccess() const;

    /**
     * @brief 检查是否为错误响应
     * @return 错误返回true
     */
    bool isError() const;

    /**
     * @brief 检查是否为URC响应
     * @return URC返回true
     */
    bool isUrc() const;

    // Getter方法
    AtResponseType getType() const { return m_type; }
    UrcType getUrcType() const { return m_urcType; }
    QString getRawResponse() const { return m_rawResponse; }
    QString getContent() const { return m_content; }
    QStringList getParameters() const { return m_parameters; }
    QString getParameter(int index) const;
    int getParameterCount() const { return m_parameters.size(); }
    QDateTime getTimestamp() const { return m_timestamp; }

    // Setter方法
    void setType(AtResponseType type) { m_type = type; }
    void setUrcType(UrcType urcType) { m_urcType = urcType; }
    void setContent(const QString& content) { m_content = content; }
    void setParameters(const QStringList& parameters) { m_parameters = parameters; }
    void addParameter(const QString& parameter) { m_parameters.append(parameter); }
    void clearParameters() { m_parameters.clear(); }

    /**
     * @brief 获取URC类型字符串
     * @param urcType URC类型枚举
     * @return URC类型字符串
     */
    static QString getUrcTypeString(UrcType urcType);

    /**
     * @brief 从字符串获取URC类型枚举
     * @param urcStr URC字符串
     * @return URC类型枚举
     */
    static UrcType getUrcTypeFromString(const QString& urcStr);

private:
    AtResponseType m_type;          // 响应类型
    UrcType m_urcType;              // URC类型
    QString m_rawResponse;          // 原始响应字符串
    QString m_content;              // 响应内容
    QStringList m_parameters;       // 参数列表
    QDateTime m_timestamp;          // 时间戳

    /**
     * @brief 解析URC响应
     * @param response 响应字符串
     */
    void parseUrc(const QString& response);

    /**
     * @brief 解析参数列表
     * @param paramPart 参数部分字符串
     */
    void parseParameters(const QString& paramPart);

    /**
     * @brief 移除字符串两端的引号
     * @param str 输入字符串
     * @return 移除引号后的字符串
     */
    QString removeQuotes(const QString& str) const;

    /**
     * @brief 清理响应字符串
     * @param response 原始响应
     * @return 清理后的响应
     */
    QString cleanResponse(const QString& response) const;
};

#endif // ATRESPONSE_H
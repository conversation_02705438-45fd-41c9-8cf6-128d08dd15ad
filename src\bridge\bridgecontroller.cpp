#include "bridgecontroller.h"
#include <QMutexLocker>
#include <QDebug>

BridgeController::BridgeController(QObject *parent)
    : QObject(parent)
    , m_serialManager(nullptr)
    , m_mqttManager(nullptr)
    , m_bridgeState(BridgeState::Stopped)
    , m_isInitialized(false)
    , m_commandTimer(nullptr)
    , m_atCommandTimeout(5000)
    , m_maxRetries(3)
    , m_nextMessageId(1)
{
    m_commandTimer = new QTimer(this);
    m_commandTimer->setSingleShot(true);

    connect(m_commandTimer, &QTimer::timeout,
            this, &BridgeController::onAtCommandTimeout);
}

BridgeController::~BridgeController()
{
    stop();
}

bool BridgeController::initialize(SerialManager* serialMgr, MqttManager* mqttMgr)
{
    QMutexLocker locker(&m_mutex);

    if (!serialMgr || !mqttMgr) {
        setLastError("串口管理器或MQTT管理器为空");
        return false;
    }

    m_serialManager = serialMgr;
    m_mqttManager = mqttMgr;

    // 连接串口管理器信号
    connect(m_serialManager, &SerialManager::connectionChanged,
            this, &BridgeController::onSerialConnectionChanged);
    connect(m_serialManager, &SerialManager::atCommandSent,
            this, &BridgeController::onAtCommandReceived);
    connect(m_serialManager, &SerialManager::atResponseReceived,
            this, &BridgeController::onAtResponseReceived);
    connect(m_serialManager, &SerialManager::urcReceived,
            this, &BridgeController::onUrcReceived);
    connect(m_serialManager, &SerialManager::errorOccurred,
            this, &BridgeController::onSerialError);

    // 连接MQTT管理器信号
    connect(m_mqttManager, &MqttManager::connectionStateChanged,
            this, &BridgeController::onMqttConnectionStateChanged);
    connect(m_mqttManager, &MqttManager::messageReceived,
            this, &BridgeController::onMqttMessageReceived);
    connect(m_mqttManager, &MqttManager::messagePublished,
            this, &BridgeController::onMqttMessagePublished);
    connect(m_mqttManager, &MqttManager::subscribed,
            this, &BridgeController::onMqttSubscribed);
    connect(m_mqttManager, &MqttManager::unsubscribed,
            this, &BridgeController::onMqttUnsubscribed);
    connect(m_mqttManager, &MqttManager::errorOccurred,
            this, &BridgeController::onMqttError);

    m_isInitialized = true;

    qInfo() << "桥接控制器初始化成功";
    return true;
}

bool BridgeController::start()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isInitialized) {
        setLastError("桥接控制器未初始化");
        return false;
    }

    if (m_bridgeState == BridgeState::Running) {
        return true; // 已经运行
    }

    setBridgeState(BridgeState::Starting);

    // 检查串口和MQTT状态
    bool serialReady = m_serialManager && m_serialManager->isConnected();
    bool mqttReady = m_mqttManager && m_mqttManager->isConnected();

    if (serialReady && mqttReady) {
        setBridgeState(BridgeState::Running);
        qInfo() << "桥接控制器启动成功";
        return true;
    } else {
        qInfo() << "桥接控制器启动中，等待连接就绪"
                << "串口:" << (serialReady ? "就绪" : "未就绪")
                << "MQTT:" << (mqttReady ? "就绪" : "未就绪");
        return true; // 启动过程中，等待连接就绪
    }
}

void BridgeController::stop()
{
    QMutexLocker locker(&m_mutex);

    if (m_bridgeState == BridgeState::Stopped) {
        return;
    }

    setBridgeState(BridgeState::Stopping);

    if (m_commandTimer && m_commandTimer->isActive()) {
        m_commandTimer->stop();
    }

    // 清空队列
    m_commandQueue.clear();
    m_pendingCommands.clear();
    m_clientStates.clear();
    m_clientHosts.clear();
    m_clientPorts.clear();

    setBridgeState(BridgeState::Stopped);
    qInfo() << "桥接控制器已停止";
}

BridgeState BridgeController::getBridgeState() const
{
    QMutexLocker locker(&m_mutex);
    return m_bridgeState;
}

bool BridgeController::isRunning() const
{
    QMutexLocker locker(&m_mutex);
    return m_bridgeState == BridgeState::Running;
}

QString BridgeController::lastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void BridgeController::setAtCommandTimeout(int timeout)
{
    QMutexLocker locker(&m_mutex);
    m_atCommandTimeout = timeout;
    qDebug() << "AT指令超时设置:" << timeout << "ms";
}

void BridgeController::setAtCommandMaxRetries(int maxRetries)
{
    QMutexLocker locker(&m_mutex);
    m_maxRetries = maxRetries;
    qDebug() << "AT指令最大重试次数设置:" << maxRetries;
}

int BridgeController::getPendingCommandCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_commandQueue.size() + m_pendingCommands.size();
}

void BridgeController::onSerialConnectionChanged(bool connected)
{
    qInfo() << "串口连接状态变化:" << (connected ? "已连接" : "已断开");

    if (connected && m_mqttManager && m_mqttManager->isConnected()) {
        if (m_bridgeState == BridgeState::Starting) {
            setBridgeState(BridgeState::Running);
            qInfo() << "桥接控制器进入运行状态";
        }
    } else {
        if (m_bridgeState == BridgeState::Running) {
            setBridgeState(BridgeState::Starting);
            qInfo() << "桥接控制器等待连接就绪";
        }
    }
}

void BridgeController::onAtCommandReceived(const AtCommand& command)
{
    if (!isRunning()) {
        qWarning() << "桥接控制器未运行，忽略AT指令:" << command.format();
        return;
    }

    qInfo() << "收到AT指令:" << command.format();
    processAtCommand(command);
}

void BridgeController::onAtResponseReceived(const AtResponse& response)
{
    qDebug() << "收到AT响应:" << response.format();
    // AT响应通常是对我们发送的指令的回复，这里可以处理确认逻辑
}

void BridgeController::onUrcReceived(const AtResponse& response)
{
    qInfo() << "收到URC:" << response.format();
    // URC通常是设备主动上报的状态，在桥接模式下较少见
}

void BridgeController::onSerialError(const QString& error)
{
    setLastError(QString("串口错误: %1").arg(error));
    qCritical() << m_lastError;

    if (m_bridgeState == BridgeState::Running) {
        setBridgeState(BridgeState::Error);
    }

    emit errorOccurred(m_lastError);
}

void BridgeController::onMqttConnectionStateChanged(MqttConnectionState state)
{
    qInfo() << "MQTT连接状态变化:" << static_cast<int>(state);

    if (state == MqttConnectionState::Connected) {
        if (m_serialManager && m_serialManager->isConnected()) {
            if (m_bridgeState == BridgeState::Starting) {
                setBridgeState(BridgeState::Running);
                qInfo() << "桥接控制器进入运行状态";
            }
        }

        // 发送MQTT连接成功URC
        sendUrc(UrcType::QMTCONN, QStringList() << "0" << "0");
    } else if (state == MqttConnectionState::Disconnected) {
        if (m_bridgeState == BridgeState::Running) {
            setBridgeState(BridgeState::Starting);
            qInfo() << "桥接控制器等待连接就绪";
        }

        // 发送MQTT断开连接URC
        sendUrc(UrcType::QMTDISC, QStringList() << "0");
    } else if (state == MqttConnectionState::Error) {
        if (m_bridgeState == BridgeState::Running) {
            setBridgeState(BridgeState::Error);
        }
    }
}

void BridgeController::onMqttMessageReceived(const QString& topic, const QByteArray& payload)
{
    qInfo() << "收到MQTT消息 - 主题:" << topic << "大小:" << payload.size();

    // 将MQTT消息转发为URC
    QStringList params;
    params << "0" << QString::number(generateMessageId()) << QString("\"%1\"").arg(topic)
           << QString::number(payload.size()) << QString("\"%1\"").arg(QString::fromUtf8(payload));

    sendUrc(UrcType::QMTRECV, params);
    emit mqttMessageForwarded(topic, payload);
}

void BridgeController::onMqttMessagePublished(const QString& topic, int messageId)
{
    qInfo() << "MQTT消息发布成功 - 主题:" << topic << "消息ID:" << messageId;

    // 发送发布成功URC
    sendUrc(UrcType::QMTPUB, QStringList() << "0" << QString::number(messageId) << "0");
}

void BridgeController::onMqttSubscribed(const QString& topic)
{
    qInfo() << "MQTT订阅成功:" << topic;

    // 发送订阅成功URC
    sendUrc(UrcType::QMTSUB, QStringList() << "0" << QString::number(generateMessageId()) << "0");
}

void BridgeController::onMqttUnsubscribed(const QString& topic)
{
    qInfo() << "MQTT取消订阅成功:" << topic;

    // 发送取消订阅成功URC
    sendUrc(UrcType::QMTUNSUB, QStringList() << "0" << QString::number(generateMessageId()));
}

void BridgeController::onMqttError(const QString& error)
{
    setLastError(QString("MQTT错误: %1").arg(error));
    qCritical() << m_lastError;

    if (m_bridgeState == BridgeState::Running) {
        setBridgeState(BridgeState::Error);
    }

    emit errorOccurred(m_lastError);
}

void BridgeController::onAtCommandTimeout()
{
    qWarning() << "AT指令处理超时";

    // 处理超时的指令
    for (auto it = m_pendingCommands.begin(); it != m_pendingCommands.end(); ++it) {
        AtCommandContext& context = it.value();
        if (context.state == AtCommandState::WaitingResponse) {
            context.state = AtCommandState::Failed;

            // 发送错误响应
            sendAtResponse(AtResponse(AtResponseType::ERROR));

            emit atCommandProcessed(context.command, false);
            qWarning() << "AT指令超时失败:" << context.command.format();
        }
    }

    // 清理超时的指令
    m_pendingCommands.clear();
}

void BridgeController::setBridgeState(BridgeState state)
{
    if (m_bridgeState != state) {
        m_bridgeState = state;
        emit bridgeStateChanged(state);
    }
}

void BridgeController::setLastError(const QString& error)
{
    m_lastError = error;
}

void BridgeController::processAtCommand(const AtCommand& command)
{
    if (!command.isValid()) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return;
    }

    // 创建指令上下文
    AtCommandContext context;
    context.command = command;
    context.messageId = generateMessageId();
    context.state = AtCommandState::Processing;

    bool success = false;

    switch (command.getCommand()) {
    case MqttAtCommand::AT:
        // 基础AT指令
        success = true;
        sendAtResponse(AtResponse(AtResponseType::OK));
        break;

    case MqttAtCommand::QMTOPEN:
        success = processQmtOpen(command, context);
        break;

    case MqttAtCommand::QMTCLOSE:
        success = processQmtClose(command, context);
        break;

    case MqttAtCommand::QMTCONN:
        success = processQmtConn(command, context);
        break;

    case MqttAtCommand::QMTDISC:
        success = processQmtDisc(command, context);
        break;

    case MqttAtCommand::QMTSUB:
        success = processQmtSub(command, context);
        break;

    case MqttAtCommand::QMTUNSUB:
        success = processQmtUnsub(command, context);
        break;

    case MqttAtCommand::QMTPUB:
        success = processQmtPub(command, context);
        break;

    case MqttAtCommand::QMTPUBEX:
        success = processQmtPubEx(command, context);
        break;

    case MqttAtCommand::QMTCFG:
        success = processQmtCfg(command, context);
        break;

    default:
        qWarning() << "不支持的AT指令:" << command.format();
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        break;
    }

    if (success) {
        context.state = AtCommandState::Completed;
        emit atCommandProcessed(command, true);
    } else {
        context.state = AtCommandState::Failed;
        emit atCommandProcessed(command, false);
    }
}

void BridgeController::sendAtResponse(const AtResponse& response)
{
    if (m_serialManager) {
        QString responseStr = response.format() + "\r\n";
        m_serialManager->sendRawData(responseStr.toUtf8());
        qDebug() << "发送AT响应:" << response.format();
    }
}

void BridgeController::sendUrc(UrcType urcType, const QStringList& parameters)
{
    if (m_serialManager) {
        AtResponse urc;
        urc.setType(AtResponseType::URC);
        urc.setUrcType(urcType);
        urc.setParameters(parameters);

        QString urcStr = urc.format() + "\r\n";
        m_serialManager->sendRawData(urcStr.toUtf8());
        qDebug() << "发送URC:" << urc.format();
    }
}

int BridgeController::generateMessageId()
{
    return m_nextMessageId++;
}

int BridgeController::getClientIndex(const QString& clientId) const
{
    Q_UNUSED(clientId)
    // 简化实现，始终使用客户端索引0
    return 0;
}

bool BridgeController::processQmtOpen(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTOPEN=<client_idx>,"<host_name>",<port>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 3) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    QString host = command.getParameter(1);
    quint16 port = command.getParameter(2).toUInt();

    // 移除引号
    if (host.startsWith('"') && host.endsWith('"')) {
        host = host.mid(1, host.length() - 2);
    }

    qInfo() << "处理QMTOPEN指令 - 客户端:" << clientIdx << "主机:" << host << "端口:" << port;

    // 保存连接信息
    m_clientHosts[clientIdx] = host;
    m_clientPorts[clientIdx] = port;

    // 尝试连接MQTT服务器
    bool success = m_mqttManager->connectToHost(host, port);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        // 连接结果将通过URC异步通知
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtClose(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTCLOSE=<client_idx>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 1) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    qInfo() << "处理QMTCLOSE指令 - 客户端:" << clientIdx;

    // 断开MQTT连接
    m_mqttManager->disconnect();

    sendAtResponse(AtResponse(AtResponseType::OK));

    // 发送关闭URC
    sendUrc(UrcType::QMTCLOSE, QStringList() << QString::number(clientIdx));

    return true;
}

bool BridgeController::processQmtConn(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTCONN=<client_idx>,"<clientid>"[,<username>,<password>]
    if (command.getType() != AtCommandType::Set || command.getParameterCount() < 2) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    QString clientId = command.getParameter(1);

    // 移除引号
    if (clientId.startsWith('"') && clientId.endsWith('"')) {
        clientId = clientId.mid(1, clientId.length() - 2);
    }

    qInfo() << "处理QMTCONN指令 - 客户端:" << clientIdx << "客户端ID:" << clientId;

    // MQTT连接通常在QMTOPEN时已经建立，这里主要是设置客户端ID
    sendAtResponse(AtResponse(AtResponseType::OK));

    return true;
}

bool BridgeController::processQmtDisc(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTDISC=<client_idx>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 1) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    qInfo() << "处理QMTDISC指令 - 客户端:" << clientIdx;

    // 断开MQTT连接
    m_mqttManager->disconnect();

    sendAtResponse(AtResponse(AtResponseType::OK));

    return true;
}

bool BridgeController::processQmtSub(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTSUB=<client_idx>,<msgid>,"<topic>",<qos>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 4) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    QString topic = command.getParameter(2);
    int qos = command.getParameter(3).toInt();

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }

    qInfo() << "处理QMTSUB指令 - 客户端:" << clientIdx << "消息ID:" << msgId
            << "主题:" << topic << "QoS:" << qos;

    // 订阅MQTT主题
    MqttQoS mqttQos = static_cast<MqttQoS>(qos);
    bool success = m_mqttManager->subscribe(topic, mqttQos);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtUnsub(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTUNSUB=<client_idx>,<msgid>,"<topic>"
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 3) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    QString topic = command.getParameter(2);

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }

    qInfo() << "处理QMTUNSUB指令 - 客户端:" << clientIdx << "消息ID:" << msgId << "主题:" << topic;

    // 取消订阅MQTT主题
    bool success = m_mqttManager->unsubscribe(topic);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtPub(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTPUB=<client_idx>,<msgid>,<qos>,<retain>,"<topic>","<msg>"
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 6) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    int qos = command.getParameter(2).toInt();
    bool retain = command.getParameter(3).toInt() != 0;
    QString topic = command.getParameter(4);
    QString message = command.getParameter(5);

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }
    if (message.startsWith('"') && message.endsWith('"')) {
        message = message.mid(1, message.length() - 2);
    }

    qInfo() << "处理QMTPUB指令 - 客户端:" << clientIdx << "消息ID:" << msgId
            << "QoS:" << qos << "保留:" << retain << "主题:" << topic << "消息:" << message;

    // 发布MQTT消息
    MqttQoS mqttQos = static_cast<MqttQoS>(qos);
    QByteArray payload = message.toUtf8();
    bool success = m_mqttManager->publish(topic, payload, mqttQos, retain);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtPubEx(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTPUBEX=<client_idx>,<msgid>,<qos>,<retain>,"<topic>",<msg_length>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 6) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    int qos = command.getParameter(2).toInt();
    bool retain = command.getParameter(3).toInt() != 0;
    QString topic = command.getParameter(4);
    int msgLength = command.getParameter(5).toInt();

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }

    qInfo() << "处理QMTPUBEX指令 - 客户端:" << clientIdx << "消息ID:" << msgId
            << "QoS:" << qos << "保留:" << retain << "主题:" << topic << "消息长度:" << msgLength;

    // QMTPUBEX需要等待后续的数据输入，这里简化处理
    // 实际实现中需要发送">"提示符，然后等待指定长度的数据
    sendAtResponse(AtResponse(AtResponseType::Prompt));

    // 这里应该等待数据输入，简化实现直接返回成功
    return true;
}

bool BridgeController::processQmtCfg(const AtCommand& command, AtCommandContext& context)
{
    // AT+QMTCFG="<cfg_type>",<client_idx>[,<cfg_value>]
    if (command.getType() != AtCommandType::Set || command.getParameterCount() < 2) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    QString cfgType = command.getParameter(0);
    int clientIdx = command.getParameter(1).toInt();

    // 移除引号
    if (cfgType.startsWith('"') && cfgType.endsWith('"')) {
        cfgType = cfgType.mid(1, cfgType.length() - 2);
    }

    qInfo() << "处理QMTCFG指令 - 配置类型:" << cfgType << "客户端:" << clientIdx;

    // 简化实现，支持基本的配置类型
    if (cfgType == "version" || cfgType == "keepalive" || cfgType == "session") {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

#include "bridgecontroller.moc"
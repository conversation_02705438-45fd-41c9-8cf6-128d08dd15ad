#include "atresponse.h"
#include <QRegularExpression>
#include <QDebug>

AtResponse::AtResponse()
    : m_type(AtResponseType::Unknown)
    , m_urcType(UrcType::Unknown)
    , m_timestamp(QDateTime::currentDateTime())
{
}

AtResponse::AtResponse(const QString& rawResponse)
    : m_type(AtResponseType::Unknown)
    , m_urcType(UrcType::Unknown)
    , m_timestamp(QDateTime::currentDateTime())
{
    parse(rawResponse);
}

AtResponse::AtResponse(AtResponseType type, const QString& content)
    : m_type(type)
    , m_urcType(UrcType::Unknown)
    , m_content(content)
    , m_timestamp(QDateTime::currentDateTime())
{
}

bool AtResponse::parse(const QString& rawResponse)
{
    m_rawResponse = rawResponse;
    m_content = cleanResponse(rawResponse);
    m_parameters.clear();
    m_timestamp = QDateTime::currentDateTime();

    if (m_content.isEmpty()) {
        return false;
    }

    QString upperContent = m_content.toUpper();

    // 检查基本响应类型
    if (upperContent == "OK") {
        m_type = AtResponseType::OK;
        return true;
    } else if (upperContent == "ERROR") {
        m_type = AtResponseType::ERROR;
        return true;
    } else if (m_content == ">") {
        m_type = AtResponseType::Prompt;
        return true;
    }

    // 检查是否为URC
    if (m_content.startsWith('+')) {
        m_type = AtResponseType::URC;
        parseUrc(m_content);
        return true;
    }

    // 其他情况视为数据响应
    m_type = AtResponseType::Data;
    return true;
}

QString AtResponse::format() const
{
    switch (m_type) {
    case AtResponseType::OK:
        return "OK";
    case AtResponseType::ERROR:
        return "ERROR";
    case AtResponseType::Prompt:
        return ">";
    case AtResponseType::URC:
        if (m_urcType != UrcType::Unknown) {
            QString result = "+" + getUrcTypeString(m_urcType);
            if (!m_parameters.isEmpty()) {
                result += ": " + m_parameters.join(",");
            }
            return result;
        }
        break;
    case AtResponseType::Data:
        return m_content;
    default:
        break;
    }

    return m_content;
}

bool AtResponse::isValid() const
{
    return m_type != AtResponseType::Unknown;
}

bool AtResponse::isSuccess() const
{
    return m_type == AtResponseType::OK;
}

bool AtResponse::isError() const
{
    return m_type == AtResponseType::ERROR;
}

bool AtResponse::isUrc() const
{
    return m_type == AtResponseType::URC;
}

QString AtResponse::getParameter(int index) const
{
    if (index >= 0 && index < m_parameters.size()) {
        return m_parameters.at(index);
    }
    return QString();
}

QString AtResponse::getUrcTypeString(UrcType urcType)
{
    switch (urcType) {
    case UrcType::QMTOPEN:    return "QMTOPEN";
    case UrcType::QMTCLOSE:   return "QMTCLOSE";
    case UrcType::QMTCONN:    return "QMTCONN";
    case UrcType::QMTDISC:    return "QMTDISC";
    case UrcType::QMTSUB:     return "QMTSUB";
    case UrcType::QMTUNSUB:   return "QMTUNSUB";
    case UrcType::QMTPUB:     return "QMTPUB";
    case UrcType::QMTPUBEX:   return "QMTPUBEX";
    case UrcType::QMTRECV:    return "QMTRECV";
    case UrcType::QMTSTAT:    return "QMTSTAT";
    case UrcType::QMTPING:    return "QMTPING";
    default:                  return "UNKNOWN";
    }
}

UrcType AtResponse::getUrcTypeFromString(const QString& urcStr)
{
    QString urc = urcStr.toUpper();

    if (urc == "QMTOPEN")    return UrcType::QMTOPEN;
    if (urc == "QMTCLOSE")   return UrcType::QMTCLOSE;
    if (urc == "QMTCONN")    return UrcType::QMTCONN;
    if (urc == "QMTDISC")    return UrcType::QMTDISC;
    if (urc == "QMTSUB")     return UrcType::QMTSUB;
    if (urc == "QMTUNSUB")   return UrcType::QMTUNSUB;
    if (urc == "QMTPUB")     return UrcType::QMTPUB;
    if (urc == "QMTPUBEX")   return UrcType::QMTPUBEX;
    if (urc == "QMTRECV")    return UrcType::QMTRECV;
    if (urc == "QMTSTAT")    return UrcType::QMTSTAT;
    if (urc == "QMTPING")    return UrcType::QMTPING;

    return UrcType::Unknown;
}

void AtResponse::parseUrc(const QString& response)
{
    // URC格式: +COMMAND: param1,param2,...
    int colonPos = response.indexOf(':');
    if (colonPos == -1) {
        // 没有参数的URC
        QString urcName = response.mid(1); // 移除+号
        m_urcType = getUrcTypeFromString(urcName);
        return;
    }

    QString urcName = response.mid(1, colonPos - 1); // 移除+号，获取URC名称
    QString paramPart = response.mid(colonPos + 1).trimmed();

    m_urcType = getUrcTypeFromString(urcName);

    if (!paramPart.isEmpty()) {
        parseParameters(paramPart);
    }
}

void AtResponse::parseParameters(const QString& paramPart)
{
    if (paramPart.isEmpty()) {
        return;
    }

    // 对于QMTRECV，需要特殊处理，因为它包含topic和payload
    if (m_urcType == UrcType::QMTRECV) {
        // +QMTRECV: <client_idx>,<msgid>,"<topic>",<payload_len>,"<payload>"
        QStringList params;
        QString current;
        bool inQuotes = false;
        int quoteCount = 0;

        for (int i = 0; i < paramPart.length(); ++i) {
            QChar ch = paramPart.at(i);

            if (ch == '"') {
                inQuotes = !inQuotes;
                quoteCount++;
                current += ch;
            } else if (ch == ',' && !inQuotes) {
                params.append(removeQuotes(current.trimmed()));
                current.clear();
            } else {
                current += ch;
            }
        }

        // 添加最后一个参数
        if (!current.isEmpty()) {
            params.append(removeQuotes(current.trimmed()));
        }

        m_parameters = params;
    } else {
        // 普通参数解析
        QStringList params;
        QString current;
        bool inQuotes = false;

        for (int i = 0; i < paramPart.length(); ++i) {
            QChar ch = paramPart.at(i);

            if (ch == '"') {
                inQuotes = !inQuotes;
                current += ch;
            } else if (ch == ',' && !inQuotes) {
                params.append(removeQuotes(current.trimmed()));
                current.clear();
            } else {
                current += ch;
            }
        }

        // 添加最后一个参数
        if (!current.isEmpty()) {
            params.append(removeQuotes(current.trimmed()));
        }

        m_parameters = params;
    }
}

QString AtResponse::removeQuotes(const QString& str) const
{
    QString result = str;
    if (result.startsWith('"') && result.endsWith('"') && result.length() >= 2) {
        result = result.mid(1, result.length() - 2);
    }
    return result;
}

QString AtResponse::cleanResponse(const QString& response) const
{
    QString cleaned = response;

    // 移除常见的行结束符
    cleaned = cleaned.remove('\r').remove('\n');

    // 移除首尾空白
    cleaned = cleaned.trimmed();

    return cleaned;
}
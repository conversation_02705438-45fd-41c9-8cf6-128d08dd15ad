#ifndef MQTTMANAGER_H
#define MQTTMANAGER_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QQueue>
#include <QMutex>
#include <QMap>
#include "mqttclient.h"
#include "appconfig.h"

/**
 * @brief MQTT消息结构体
 */
struct MqttMessage {
    QString topic;          // 主题
    QByteArray payload;     // 负载
    MqttQoS qos;           // 服务质量
    bool retain;           // 是否保留

    MqttMessage()
        : qos(MqttQoS::AtLeastOnce)
        , retain(false)
    {}

    MqttMessage(const QString& t, const QByteArray& p, MqttQoS q = MqttQoS::AtLeastOnce, bool r = false)
        : topic(t), payload(p), qos(q), retain(r)
    {}
};

/**
 * @brief MQTT管理器类
 *
 * 负责MQTT客户端的高级管理和消息处理
 * 提供消息队列、重试机制、状态管理等功能
 */
class MqttManager : public QObject
{
    Q_OBJECT

public:
    explicit MqttManager(QObject *parent = nullptr);
    ~MqttManager();

    /**
     * @brief 初始化MQTT管理器
     * @param config MQTT配置
     * @return 初始化成功返回true
     */
    bool initialize(const MqttConfig& config);

    /**
     * @brief 启动MQTT管理器
     * @return 启动成功返回true
     */
    bool start();

    /**
     * @brief 停止MQTT管理器
     */
    void stop();

    /**
     * @brief 检查是否已连接
     * @return 已连接返回true
     */
    bool isConnected() const;

    /**
     * @brief 连接到MQTT服务器
     * @param host 服务器地址
     * @param port 服务器端口
     * @return 连接成功返回true
     */
    bool connectToHost(const QString& host, quint16 port);

    /**
     * @brief 断开MQTT连接
     */
    void disconnect();

    /**
     * @brief 订阅主题
     * @param topic 主题名称
     * @param qos 服务质量
     * @return 订阅成功返回true
     */
    bool subscribe(const QString& topic, MqttQoS qos = MqttQoS::AtLeastOnce);

    /**
     * @brief 取消订阅主题
     * @param topic 主题名称
     * @return 取消订阅成功返回true
     */
    bool unsubscribe(const QString& topic);

    /**
     * @brief 发布消息
     * @param topic 主题名称
     * @param payload 消息内容
     * @param qos 服务质量
     * @param retain 是否保留消息
     * @return 发布成功返回true
     */
    bool publish(const QString& topic, const QByteArray& payload,
                MqttQoS qos = MqttQoS::AtLeastOnce, bool retain = false);

    /**
     * @brief 发布消息（使用消息结构体）
     * @param message MQTT消息
     * @return 发布成功返回true
     */
    bool publish(const MqttMessage& message);

    /**
     * @brief 获取MQTT客户端
     * @return MQTT客户端指针
     */
    IMqttClient* getMqttClient() const;

    /**
     * @brief 获取连接状态
     * @return 连接状态
     */
    MqttConnectionState getConnectionState() const;

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString lastError() const;

    /**
     * @brief 设置消息重试
     * @param enabled 是否启用重试
     * @param maxRetries 最大重试次数
     * @param retryInterval 重试间隔(毫秒)
     */
    void setMessageRetry(bool enabled, int maxRetries = 3, int retryInterval = 1000);

    /**
     * @brief 设置消息队列大小
     * @param maxSize 最大队列大小
     */
    void setMessageQueueSize(int maxSize);

    /**
     * @brief 获取已订阅的主题列表
     * @return 主题列表
     */
    QStringList getSubscribedTopics() const;

    /**
     * @brief 获取待发送消息数量
     * @return 消息数量
     */
    int getPendingMessageCount() const;

signals:
    /**
     * @brief 连接状态变化信号
     * @param state 连接状态
     */
    void connectionStateChanged(MqttConnectionState state);

    /**
     * @brief 连接成功信号
     */
    void connected();

    /**
     * @brief 断开连接信号
     */
    void disconnected();

    /**
     * @brief 消息接收信号
     * @param topic 主题名称
     * @param payload 消息内容
     */
    void messageReceived(const QString& topic, const QByteArray& payload);

    /**
     * @brief 消息发布成功信号
     * @param topic 主题名称
     * @param messageId 消息ID
     */
    void messagePublished(const QString& topic, int messageId);

    /**
     * @brief 订阅成功信号
     * @param topic 主题名称
     */
    void subscribed(const QString& topic);

    /**
     * @brief 取消订阅成功信号
     * @param topic 主题名称
     */
    void unsubscribed(const QString& topic);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);

private slots:
    /**
     * @brief 处理MQTT连接状态变化
     * @param state 连接状态
     */
    void onMqttConnectionStateChanged(MqttConnectionState state);

    /**
     * @brief 处理MQTT消息接收
     * @param topic 主题名称
     * @param payload 消息内容
     */
    void onMqttMessageReceived(const QString& topic, const QByteArray& payload);

    /**
     * @brief 处理MQTT消息发布成功
     * @param messageId 消息ID
     */
    void onMqttMessagePublished(int messageId);

    /**
     * @brief 处理MQTT订阅成功
     * @param topic 主题名称
     */
    void onMqttSubscribed(const QString& topic);

    /**
     * @brief 处理MQTT取消订阅成功
     * @param topic 主题名称
     */
    void onMqttUnsubscribed(const QString& topic);

    /**
     * @brief 处理MQTT错误
     * @param error 错误信息
     */
    void onMqttError(const QString& error);

    /**
     * @brief 处理消息队列
     */
    void processMessageQueue();

    /**
     * @brief 处理重试定时器超时
     */
    void onRetryTimeout();

private:
    MqttClient* m_mqttClient;           // MQTT客户端
    MqttConfig m_config;                // MQTT配置
    QString m_lastError;                // 最后的错误信息
    bool m_isInitialized;               // 是否已初始化

    // 消息队列和重试机制
    QQueue<MqttMessage> m_messageQueue; // 消息队列
    QTimer* m_queueTimer;               // 队列处理定时器
    QTimer* m_retryTimer;               // 重试定时器
    bool m_retryEnabled;                // 是否启用重试
    int m_maxRetries;                   // 最大重试次数
    int m_retryInterval;                // 重试间隔
    int m_maxQueueSize;                 // 最大队列大小

    // 消息跟踪
    QMap<int, MqttMessage> m_pendingMessages; // 待确认消息
    QMap<QString, int> m_topicRetryCount;     // 主题重试计数

    mutable QMutex m_mutex;             // 线程安全锁

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setLastError(const QString& error);

    /**
     * @brief 将消息加入队列
     * @param message MQTT消息
     */
    void enqueueMessage(const MqttMessage& message);

    /**
     * @brief 重试发送消息
     * @param message MQTT消息
     */
    void retryMessage(const MqttMessage& message);

    /**
     * @brief 清理消息队列
     */
    void clearMessageQueue();
};

#endif // MQTTMANAGER_H
#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "configmanager.h"
#include "serialmanager.h"
#include "mqttmanager.h"
#include "bridgecontroller.h"

int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);

    qInfo() << "串口MQTT桥接程序启动...";

    // 初始化配置管理器
    ConfigManager* configMgr = ConfigManager::instance();

    // 创建管理器和桥接控制器
    SerialManager* serialMgr = new SerialManager(&a);
    MqttManager* mqttMgr = new MqttManager(&a);
    BridgeController* bridgeCtrl = new BridgeController(&a);

    // 连接配置加载信号
    QObject::connect(configMgr, &ConfigManager::configLoaded, [&](bool success) {
        if (success) {
            qInfo() << "配置文件加载成功";

            // 获取并显示配置信息
            ConfigManager* mgr = ConfigManager::instance();
            SerialConfig serialConfig = mgr->getSerialConfig();
            LogConfig logConfig = mgr->getLogConfig();
            MqttConfig mqttConfig = mgr->getMqttConfig();

            qInfo() << "串口配置:";
            qInfo() << "  端口:" << serialConfig.portName;
            qInfo() << "  波特率:" << serialConfig.baudRate;
            qInfo() << "  读超时:" << serialConfig.readTimeout << "ms";

            qInfo() << "日志配置:";
            qInfo() << "  级别:" << logConfig.logLevel;
            qInfo() << "  文件路径:" << logConfig.logFilePath;
            qInfo() << "  控制台输出:" << logConfig.enableConsoleOutput;

            qInfo() << "MQTT配置:";
            qInfo() << "  主机:" << mqttConfig.host;
            qInfo() << "  端口:" << mqttConfig.port;
            qInfo() << "  客户端ID:" << mqttConfig.clientId;

            // 显示可用串口
            QStringList availablePorts = SerialManager::getAvailablePorts();
            qInfo() << "可用串口:" << availablePorts;

            // 初始化串口管理器
            if (serialMgr->initialize(serialConfig)) {
                qInfo() << "串口管理器初始化成功";

                // 设置自动重连
                serialMgr->setAutoReconnect(true, 3000);

                // 尝试启动串口
                if (serialMgr->start()) {
                    qInfo() << "串口启动成功";

                    // 发送测试AT指令
                    QTimer::singleShot(1000, [serialMgr]() {
                        if (serialMgr->sendAtCommand("AT")) {
                            qInfo() << "发送AT测试指令成功";
                        } else {
                            qWarning() << "发送AT测试指令失败:" << serialMgr->lastError();
                        }
                    });
                } else {
                    qWarning() << "串口启动失败:" << serialMgr->lastError();
                }
            } else {
                qCritical() << "串口管理器初始化失败:" << serialMgr->lastError();
            }

            // 初始化MQTT管理器
            if (mqttMgr->initialize(mqttConfig)) {
                qInfo() << "MQTT管理器初始化成功";

                // 启动MQTT管理器
                if (mqttMgr->start()) {
                    qInfo() << "MQTT管理器启动成功";

                    // 尝试连接MQTT服务器
                    QTimer::singleShot(2000, [mqttMgr, mqttConfig]() {
                        if (mqttMgr->connectToHost(mqttConfig.host, mqttConfig.port)) {
                            qInfo() << "MQTT连接请求已发送";

                            // 连接成功后测试订阅和发布
                            QTimer::singleShot(3000, [mqttMgr]() {
                                if (mqttMgr->isConnected()) {
                                    // 测试订阅
                                    if (mqttMgr->subscribe("test/topic", MqttQoS::AtLeastOnce)) {
                                        qInfo() << "测试订阅成功";
                                    }

                                    // 测试发布
                                    QByteArray testPayload = "Hello MQTT from SerialMqttBridge!";
                                    if (mqttMgr->publish("test/topic", testPayload)) {
                                        qInfo() << "测试发布成功";
                                    }
                                } else {
                                    qWarning() << "MQTT未连接，无法测试";
                                }
                            });
                        } else {
                            qWarning() << "MQTT连接失败:" << mqttMgr->lastError();
                        }
                    });
                } else {
                    qWarning() << "MQTT管理器启动失败:" << mqttMgr->lastError();
                }
            } else {
                qCritical() << "MQTT管理器初始化失败:" << mqttMgr->lastError();
            }

            // 初始化桥接控制器
            if (bridgeCtrl->initialize(serialMgr, mqttMgr)) {
                qInfo() << "桥接控制器初始化成功";

                // 启动桥接控制器
                if (bridgeCtrl->start()) {
                    qInfo() << "桥接控制器启动成功";

                    // 测试AT指令处理
                    QTimer::singleShot(5000, [bridgeCtrl, serialMgr]() {
                        if (bridgeCtrl->isRunning()) {
                            qInfo() << "测试AT指令处理...";

                            // 模拟发送AT指令
                            if (serialMgr->sendAtCommand("AT+QMTOPEN=0,\"localhost\",1883")) {
                                qInfo() << "发送QMTOPEN指令成功";
                            }

                            QTimer::singleShot(2000, [serialMgr]() {
                                if (serialMgr->sendAtCommand("AT+QMTCONN=0,\"TestClient\"")) {
                                    qInfo() << "发送QMTCONN指令成功";
                                }
                            });

                            QTimer::singleShot(4000, [serialMgr]() {
                                if (serialMgr->sendAtCommand("AT+QMTSUB=0,1,\"test/topic\",1")) {
                                    qInfo() << "发送QMTSUB指令成功";
                                }
                            });
                        } else {
                            qWarning() << "桥接控制器未运行，无法测试";
                        }
                    });
                } else {
                    qWarning() << "桥接控制器启动失败:" << bridgeCtrl->lastError();
                }
            } else {
                qCritical() << "桥接控制器初始化失败:" << bridgeCtrl->lastError();
            }
        } else {
            qCritical() << "配置文件加载失败";
        }
    });

    // 连接串口管理器信号
    QObject::connect(serialMgr, &SerialManager::connectionChanged, [](bool connected) {
        qInfo() << "串口连接状态:" << (connected ? "已连接" : "已断开");
    });

    QObject::connect(serialMgr, &SerialManager::atResponseReceived, [](const AtResponse& response) {
        qInfo() << "收到AT响应:" << response.format();
    });

    QObject::connect(serialMgr, &SerialManager::urcReceived, [](const AtResponse& response) {
        qInfo() << "收到URC:" << response.format();
    });

    QObject::connect(serialMgr, &SerialManager::errorOccurred, [](const QString& error) {
        qWarning() << "串口错误:" << error;
    });

    // 连接MQTT管理器信号
    QObject::connect(mqttMgr, &MqttManager::connectionStateChanged, [](MqttConnectionState state) {
        QString stateStr;
        switch (state) {
        case MqttConnectionState::Disconnected: stateStr = "已断开"; break;
        case MqttConnectionState::Connecting: stateStr = "连接中"; break;
        case MqttConnectionState::Connected: stateStr = "已连接"; break;
        case MqttConnectionState::Disconnecting: stateStr = "断开中"; break;
        case MqttConnectionState::Error: stateStr = "错误"; break;
        }
        qInfo() << "MQTT连接状态:" << stateStr;
    });

    QObject::connect(mqttMgr, &MqttManager::messageReceived, [](const QString& topic, const QByteArray& payload) {
        qInfo() << "收到MQTT消息 - 主题:" << topic << "内容:" << QString::fromUtf8(payload);
    });

    QObject::connect(mqttMgr, &MqttManager::messagePublished, [](const QString& topic, int messageId) {
        qInfo() << "MQTT消息发布成功 - 主题:" << topic << "消息ID:" << messageId;
    });

    QObject::connect(mqttMgr, &MqttManager::subscribed, [](const QString& topic) {
        qInfo() << "MQTT订阅成功:" << topic;
    });

    QObject::connect(mqttMgr, &MqttManager::errorOccurred, [](const QString& error) {
        qWarning() << "MQTT错误:" << error;
    });

    // 连接桥接控制器信号
    QObject::connect(bridgeCtrl, &BridgeController::bridgeStateChanged, [](BridgeState state) {
        QString stateStr;
        switch (state) {
        case BridgeState::Stopped: stateStr = "已停止"; break;
        case BridgeState::Starting: stateStr = "启动中"; break;
        case BridgeState::Running: stateStr = "运行中"; break;
        case BridgeState::Stopping: stateStr = "停止中"; break;
        case BridgeState::Error: stateStr = "错误"; break;
        }
        qInfo() << "桥接状态:" << stateStr;
    });

    QObject::connect(bridgeCtrl, &BridgeController::atCommandProcessed, [](const AtCommand& command, bool success) {
        qInfo() << "AT指令处理" << (success ? "成功" : "失败") << ":" << command.format();
    });

    QObject::connect(bridgeCtrl, &BridgeController::mqttMessageForwarded, [](const QString& topic, const QByteArray& payload) {
        qInfo() << "MQTT消息转发 - 主题:" << topic << "内容:" << QString::fromUtf8(payload);
    });

    QObject::connect(bridgeCtrl, &BridgeController::errorOccurred, [](const QString& error) {
        qWarning() << "桥接错误:" << error;
    });

    // 加载配置文件
    if (!configMgr->loadConfig()) {
        qCritical() << "无法加载配置文件，程序退出";
        return -1;
    }

    qInfo() << "程序初始化完成，按Ctrl+C退出";

    // 20秒后自动退出（用于测试）
    QTimer::singleShot(20000, &a, [&a, serialMgr, mqttMgr, bridgeCtrl]() {
        qInfo() << "测试完成，停止所有管理器";
        bridgeCtrl->stop();
        serialMgr->stop();
        mqttMgr->stop();
        qInfo() << "程序退出";
        a.quit();
    });

    return a.exec();
}

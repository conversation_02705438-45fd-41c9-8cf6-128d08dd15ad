# MCU与MQTT模块交互协议说明_V1

| **版本** | **日期**  | **编者** | **说明** |
| -------- | --------- | -------- | -------- |
| V1.0     | 2025/7/14 |          | 初始版本 |


## 1. 概述

本指南旨在为开发者提供一个清晰的、关于主控单元 (MCU) 如何通过AT指令与MQTT模块进行MQTT通信的详细流程。文档聚焦于**可靠消息传输（QoS 1）** 场景下的指令交互、报文格式以及核心时序。

所有交互都基于MCU作为指令发起方（Master），MQTT模块作为指令接收与执行方（Slave）的模式。

## 2. AT指令帧格式

为了确保MCU与MQTT模块之间的正确通信，所有AT指令都必须遵循特定的帧格式。

- MCU发送格式:
  
    MCU发送给模块的每一条AT指令都必须以回车符 `<CR>` (ASCII 0x0D) 结束。在实际应用中，通常发送回车换行符  `<CR><LF>`(ASCII 0x0D 0x0A) 以获得更好的兼容性。
    
    ```
    AT...<CR><LF>
    ```
    
- 模块响应格式:
  
    模块返回的响应数据通常被回车换行符 `<CR><LF>`包围。这包括即时响应（如 OK）和异步上报的URC（非请求结果码）。
    
    ```
    <CR><LF>response<CR><LF>
    ```
    

> **重要提示**: 在本文后续的所有交互示例中，为了简洁起见，将省略 `<CR><LF>` 字符，但开发者在实际编码时**必须**在每条AT指令的末尾添加它们。

## 3. 总体交互时序图

下图展示了从初始化到断开连接的完整MQTT生命周期中，MCU、MQTT模块以及MQTT服务器三者之间的核心交互时序。

以下所有指令，如果指令出错（格式错误等导致指令无法正常运行的情况），模块只回复“ERROR”，错误分支不再画在下面的时序图中了。

```mermaid
sequenceDiagram
    participant MCU
    participant MQTT Modem
    participant MQTT Server

    Note over MCU,MQTT Modem: 1. 确认模块就绪
    MCU->>MQTT Modem: AT
    MQTT Modem-->>MCU: OK

    Note over MCU,MQTT Modem: 2. 配置模块
    MCU->>MQTT Modem:MP257暂时不涉及<br>有待实际使用EC20后完善
    MQTT Modem-->>MCU: OK

    Note over MCU,MQTT Server: 3. 打开客户端网络
    MCU->>MQTT Modem: AT+QMTOPEN=0,"test.mosquitto.org",1883
    MQTT Modem-->>MCU: OK
    MQTT Modem->>MQTT Server: TCP Handshake
    MQTT Server-->>MQTT Modem: 
    MQTT Modem-->>MCU: +QMTOPEN: 0,0
    Note over MCU,MQTT Modem: 查询网络状态(可选)
    MCU->>MQTT Modem: AT+QMTOPEN?
    MQTT Modem-->>MCU: +QMTOPEN: 0,"test.mosquitto.org",1883
    MQTT Modem-->>MCU:OK

    Note over MCU,MQTT Server: 4. 建立连接
    MCU->>MQTT Modem: AT+QMTCONN=0,"MCU_Client_001"
    MQTT Modem-->>MCU: OK
    MQTT Modem->>MQTT Server: CONNECT
    MQTT Server-->>MQTT Modem: CONNACK
    MQTT Modem-->>MCU: +QMTCONN: 0,0,0
    Note over MCU,MQTT Modem: 查询连接状态(可选)
    MCU->>MQTT Modem: AT+QMTCONN?
    MQTT Modem-->>MCU: +QMTCONN: 0,3
	MQTT Modem-->>MCU:OK
	
    Note over MCU,MQTT Server: 5. 订阅主题
    MCU->>MQTT Modem: AT+QMTSUB=0,1,"topic/command",1
    MQTT Modem-->>MCU: OK
    MQTT Modem->>MQTT Server: SUBSCRIBE (Packet ID: 1)
    MQTT Server-->>MQTT Modem: SUBACK (Packet ID: 1)
    MQTT Modem-->>MCU: +QMTSUB: 0,1,0,1
    Note over MCU,MQTT Modem: 查询订阅(可选)
    MCU->>MQTT Modem: AT+QMTSUB=?
    MQTT Modem-->>MCU: +QMTSUB: 0,1,"topic/command",1
	MQTT Modem-->>MCU:OK
	
    Note over MCU,MQTT Server: 6. 数据收发 (异步事件)
    opt 上行发布流程
    MCU->>MQTT Modem: AT+QMTPUBEX=0,2,1,0,"topic/data",18
    MQTT Modem-->>MCU: >
    MCU->>MQTT Modem: {"temp":25.4,"hum":60}
    MQTT Modem-->>MCU: OK
    MQTT Modem->>MQTT Server: PUBLISH (Packet ID: 2)
    MQTT Server-->>MQTT Modem: PUBACK (Packet ID: 2)
    MQTT Modem-->>MCU: +QMTPUBEX: 0,2,0
    end
    opt 下行接收流程
    MQTT Server->>MQTT Modem: PUBLISH (Packet ID: 3)
    MQTT Modem-->>MCU: +QMTRECV: 0,3,"topic/command",11,"SET_FREQ:10"
    MQTT Modem->>MQTT Server: PUBACK (Packet ID: 3)
    end

    Note over MCU,MQTT Server: 7. 断开连接
    MCU->>MQTT Modem: AT+QMTDISC=0
    MQTT Modem-->>MCU: OK
    MQTT Modem->>MQTT Server: DISCONNECT
    MQTT Modem-->>MCU: +QMTDISC: 0,0

    MCU->>MQTT Modem: AT+QMTCLOSE=0
    MQTT Modem-->>MCU: OK
    MQTT Modem-->>MCU: +QMTCLOSE: 0,0
```

## 4. 交互过程详解

### 阶段一：确认模块就绪

此阶段的目标是确保MCU与MQTT模块之间的物理通信正常，为后续操作奠定基础。

- **交互说明**: MCU向模块发送`AT`指令。如果模块返回`OK`，则表示模块已上电且AT指令串口工作正常。这是所有AT交互的“握手”步骤。
  
|         |           |                      |
| ------- | --------- | -------------------- |
| **发送方** | **报文/指令** | **描述**               |
| MCU     | `AT`      | 发送基础AT指令，测试通信链路。     |
| MQTT模块  | `OK`      | 模块正常响应，表示已准备好接收后续指令。 |

### 阶段二：配置模块

MP257暂不涉及。

对于低功耗设备（如每小时上报一次数据），核心目标是**最小化网络连接时间**以节省电量。因此，推荐采用“**用完即断**”的短连接策略，而非维持长连接。本阶段的配置围绕此策略展开。所有配置均为非易失性，模块重启后需重新配置。

- **交互说明**: MCU使用`AT+QMTCFG`指令对MQTT客户端进行初始化设置。合理的配置是稳定通信的保障。
  
|                |      |                                                   |                                                              |
| -------------- | ---- | ------------------------------------------------- | ------------------------------------------------------------ |
| **配置项**     |      | **示例指令**                                      | **描述与注意**                                               |
| **会话类型**   |      | `AT+QMTCFG="session",0,0`                         | 设置为持久会话 (`clean_session=0`)。服务器会保留离线消息，**MCU必须处理重连后可能收到的旧消息**。 |
| **遗嘱消息**   |      | `AT+QMTCFG="will",0,1,1,0,"dev/status","offline"` | 当设备异常掉线时，服务器将代表设备发布一条预设消息。         |
| **超时与重试** |      | `AT+QMTCFG="timeout",0,5,3,1`                     | 设置QoS 1/2消息的传输超时为5秒，重试3次。是实现可靠传输的重要保障。 |
| **接收模式**   |      | `AT+QMTCFG="recv/mode",0,0,1`                     | 设置消息接收为URC主动上报模式，MCU无需轮询即可获取下行数据。 |

### 阶段三：打开客户端网络

此阶段为MQTT通信建立底层的TCP/IP网络连接。

- **交互说明**: MCU发送`AT+QMTOPEN`指令，请求模块连接到指定的MQTT服务器地址和端口。这是一个异步过程，MCU发送指令后会立即收到`OK`，表示指令已被接收。模块随后进行TCP三次握手，成功建立连接后，会通过一个URC `+QMTOPEN`通知MCU网络已就绪。
  
- **指令格式与参数说明**:
  
    ```
    AT+QMTOPEN=<client_idx>,"<host_name>",<port>
    ```
    

| **参数**          | **释义**   | **类型/格式** | **说明**                   |
| --------------- | -------- | --------- | ------------------------ |
| `<client_idx>`  | 客户端标识符索引 | 整型        | 范围 0-5，用于区分不同的MQTT客户端实例。 |
| `"<host_name>"` | 服务器地址    | 字符串       | MQTT服务器的域名或IP地址。         |
| `<port>`        | 服务器端口    | 整型        | MQTT服务器的端口号，非加密通常为1883。  |

- **模块响应**:
  
    - 立即响应：`OK`
      
    - 异步URC：`+QMTOPEN: <client_idx>,<result>`。`<result>`为0表示成功。
    
- **示例**:
  
    ```
    AT+QMTOPEN=0,"test.mosquitto.org",1883
    ```
    
    _上述示例为客户端 `0` 打开一个到服务器 `test.mosquitto.org` 端口 `1883` 的网络连接。_
    
- **查询指令 (可选)**:
  
    - **交互说明**: 查询指定客户端当前的网络打开状态。
      
    - **指令格式**: `AT+QMTOPEN?`
      
    - **模块响应**:
      
        ```
        +QMTOPEN: <client_idx>,"<host_name>",<port>
        
        OK
        ```
        
        _如果指定客户端网络未打开，则不返回 `+QMTOPEN` 信息行，仅返回 `OK`。_
        

### 阶段四：建立连接

在TCP网络连接的基础上，此阶段进行MQTT协议层的连接。

- **交互说明**: MCU发送`AT+QMTCONN`指令，携带客户端ID等认证信息发起MQTT协议层的连接请求。模块将这些信息封装成MQTT的`CONNECT`报文发送给服务器。服务器验证通过后返回`CONNACK`报文，模块在收到后，通过URC `+QMTCONN` 通知MCU连接已成功建立。
  
- **指令格式与参数说明**:
  
    ```
    AT+QMTCONN=<client_idx>,"<clientid>"[,<username>,<password>]
    ```
    
|                |              |           |                                 |
| -------------- | ------------ | --------- | ------------------------------- |
| **参数**         | **释义**       | **类型/格式** | **说明**                          |
| `<client_idx>` | 客户端标识符索引     | 整型        | 范围 0-5，与 `AT+QMTOPEN` 中使用的保持一致。 |
| `"<clientid>"` | 客户端唯一标识符     | 字符串       | MQTT协议要求的客户端ID，在服务器上必须唯一。       |
| `"<username>"` | **(可选)** 用户名 | 字符串       | 用于服务器认证。                        |
| `"<password>"` | **(可选)** 密码  | 字符串       | 与用户名配对的密码，用于服务器认证。              |

- **模块响应**:
  
    - 立即响应：`OK`
      
    - 异步URC：`+QMTCONN: <client_idx>,<result>[,<ret_code>]`。`<result>`为0且`<ret_code>`为0表示成功。
    
- **示例**:
  
    ```
    AT+QMTCONN=0,"MCU_Client_001"
    ```
    
    _上述示例为客户端 `0` 使用ID `"MCU_Client_001"` 进行连接，省略了可选的用户名和密码。_
    
- **查询指令 (可选)**:
  
    - **交互说明**: 查询指定客户端当前的MQTT连接状态。
      
    - **指令格式**: `AT+QMTCONN?`
      
    - **模块响应**:
      
        ```
        +QMTCONN: <client_idx>,<state>
        
        OK
        ```
        
    - `<state>` 值说明:

| `<state>`值 | 含义          |
| :--------- | :---------- |
| 1          | MQTT 初始化    |
| 2          | MQTT 正在连接   |
| 3          | MQTT 已经连接成功 |
| 4          | MQTT 正在断开连接 |


### 阶段五：订阅主题

此阶段通知服务器，客户端希望接收哪些主题的消息。

- **交互说明**: MCU发送`AT+QMTSUB`指令，指定要订阅的主题名称和期望的服务质量（QoS）等级。模块向服务器发送`SUBSCRIBE`报文。服务器处理后返回`SUBACK`报文，确认订阅请求。模块最终通过URC `+QMTSUB` 将结果通知MCU。
  
- **指令格式与参数说明**:
  
    ```
    AT+QMTSUB=<client_idx>,<msgid>,"<topic>",<qos>
    ```
    
|   |   |   |   |
|---|---|---|---|
|**参数**|**释义**|**类型/格式**|**说明**|
|`<client_idx>`|客户端标识符索引|整型|范围 0-5。|
|`<msgid>`|消息ID|整型|范围 1-65535。MCU需保证“飞行中”的消息ID唯一。|
|`"<topic>"`|订阅的主题|字符串|要接收消息的主题名称。|
|`<qos>`|服务质量等级|整型|本指南聚焦于QoS 1，表示至少一次的可靠传输。|

- **模块响应**:
  
    - 立即响应：`OK`
      
    - 异步URC：`+QMTSUB: <client_idx>,<msgid>,<result>[,<value>]`。`<result>`为0表示成功。
    
- **示例**:
  
    ```
    AT+QMTSUB=0,1,"topic/command",1
    ```
    
    _上述示例为客户端 `0` 使用消息ID `1` 订阅主题 `"topic/command"`，QoS等级为 `1`。_
    
- **查询指令 (可选)**:
  
    - **交互说明**: MQTT协议本身不提供查询当前已订阅主题列表的功能，因此没有类似`AT+QMTSUB?`的查询指令。开发者需要在MCU侧自行维护一份订阅列表。
      
    - **测试指令**: 可以使用测试指令`AT+QMTSUB=?`来查询模块支持的参数范围和格式。
      
    - **模块响应**:
      
        ```
        +QMTSUB:(支持的<client_idx>范围),<msgid>,list of ["topic",qos]
        
        OK
        ```
        

### 阶段六：基于主题的QOS1数据收发

此阶段是MQTT通信的核心，包含上行（发布）和下行（接收）两个方向的数据交互。

- **上行发布 (Publish)**
  
    - **交互说明**: MCU使用`AT+QMTPUBEX`指令发布数据。模块响应`>`后，MCU发送实际数据。模块将数据封装成`PUBLISH`报文发送，并在收到服务器的`PUBACK`确认后，通过URC `+QMTPUBEX` 通知MCU发布成功。
      
    - **指令格式与参数说明**:
      
        ```
        AT+QMTPUBEX=<client_idx>,<msgid>,<qos>,<retain>,"<topic>",<msg_length>
        ```


|                |          |           |                               |
| -------------- | -------- | --------- | ----------------------------- |
| **参数**         | **释义**   | **类型/格式** | **说明**                        |
| `<client_idx>` | 客户端标识符索引 | 整型        | 范围 0-5。                       |
| `<msgid>`      | 消息ID     | 整型        | 范围 1-65535，用于QoS 1/2消息的端到端确认。 |
| `<qos>`        | 服务质量等级   | 整型        | 0, 1, 或 2。本指南聚焦于 `1`。         |
| `<retain>`     | 保留消息标志   | 整型        | 0: 不保留; 1: 保留。                |
| `<topic>`      | 发布的主题    | 字符串       | 数据要发布到的主题名称。                  |
| `<msg_length>` | 消息长度     | 整型        | 要发送数据的字节数。                    |


- **模块响应**:
	
	- 立即响应：`>`，提示MCU可以发送数据。
		
	- 发送数据后响应：`OK`。
		
	- 异步URC：`+QMTPUBEX: <client_idx>,<msgid>,<result>`。`<result>`为0表示成功。
	
- **示例**:
	
	```
	MCU: AT+QMTPUBEX=0,2,1,0,"topic/data",18
	Modem: >
	MCU: {"temp":25.4,"hum":60}
	```
	
	_上述示例向主题 `"topic/data"` 发布一条18字节的QoS 1消息。_
	
- **下行接收 (Receive)**
  
    - **交互说明**: 当服务器有消息推送到客户端订阅的主题时，模块会收到`PUBLISH`报文，并自动回复`PUBACK`。随后，模块通过URC `+QMTRECV` 将收到的消息内容（主题、负载等）主动上报给MCU。
      
    - **URC格式**: `+QMTRECV: <client_idx>,<msgid>,"<topic>",<payload_len>,"<payload>"`
      

### 阶段七：断开连接

此阶段用于安全、有序地终止MQTT会话和网络连接。

- **步骤1：断开MQTT连接**
  
    - **交互说明**: MCU发送`AT+QMTDISC`。模块向服务器发送`DISCONNECT`报文，并上报`+QMTDISC: 0,0`。
      
    - **指令格式与参数说明**:
      
        ```
        AT+QMTDISC=<client_idx>
        ```
        
|   |   |   |   |
|---|---|---|---|
|**参数**|**释义**|**类型/格式**|**说明**|
|`<client_idx>`|客户端标识符索引|整型|范围 0-5，指定要断开的客户端。|

- **模块响应**:
	
	- 立即响应：`OK`
		
	- 异步URC：`+QMTDISC: <client_idx>,<result>`。`<result>`为0表示成功。
		
	- 状态变化URC：`+QMTSTAT: <client_idx>,5`。错误码5表示客户端主动正常断开。
	
- **示例**: `AT+QMTDISC=0`
  
- **步骤2：关闭网络**
  
    - **交互说明**: MCU发送`AT+QMTCLOSE`，彻底释放TCP连接。模块在完成后上报`+QMTCLOSE: 0,0`。
      
    - **指令格式与参数说明**:
      
        ```
        AT+QMTCLOSE=<client_idx>
        ```
        
|   |   |   |   |
|---|---|---|---|
|**参数**|**释义**|**类型/格式**|**说明**|
|`<client_idx>`|客户端标识符索引|整型|范围 0-5，指定要关闭网络的客户端。|

- **模块响应**:
	
	- 立即响应：`OK`
		
	- 异步URC：`+QMTCLOSE: <client_idx>,<result>`。`<result>`为0表示成功。
	
- **示例**: `AT+QMTCLOSE=0`
	

## 5. URC (非请求结果码) 详解

URC 是模块主动上报给 MCU 的信息，用于通知异步事件的发生，是实现稳定通信的关键。MCU的程序必须能够正确解析这些 URC。

### 5.1 `+QMTRECV`: 接收到服务器数据

- **交互说明**: 当模块接收到服务器发布的PUBLISH消息时上报。这是MCU获取下行数据的核心途径。
  
- **URC格式与参数说明**:
  
    ```
    +QMTRECV: <client_idx>,<msgid>,"<topic>",<payload_len>,"<payload>"
    ```
    
|   |   |   |   |
|---|---|---|---|
|**参数**|**释义**|**类型/格式**|**说明**|
|`<client_idx>`|客户端标识符索引|整型|范围 0-5。|
|`<msgid>`|消息ID|整型|范围 0-65535。|
|`"<topic>"`|消息所属的主题|字符串|服务器下发消息所在的主题。|
|`<payload_len>`|消息负载长度|整型|`<payload>`的字节数。|
|`"<payload>"`|消息负载|字符串|消息的实际内容。|


### 5.2 `+QMTSTAT`: MQTT 链路层状态变化

- **交互说明**: 当MQTT连接状态发生意外改变时（如被服务器踢下线、心跳超时等）上报。MCU应根据此代码执行重连等恢复逻辑。
  
- **URC格式与参数说明**:
  
    ```
    +QMTSTAT: <client_idx>,<err_code>
    ```
    
|   |   |   |   |
|---|---|---|---|
|**参数**|**释义**|**类型/格式**|**说明**|
|`<client_idx>`|客户端标识符索引|整型|范围 0-5。|
|`<err_code>`|错误代码|整型|表示状态变化的具体原因，详见下表。|

- **`<err_code>` 详细说明**:
  

|   |   |   |   |
|---|---|---|---|
|**<err_code>值**|**含义**|**建议解决方法**|**MCU程序状态建议**|
|1|连接被服务器断开或重置|执行完整的重连流程（CLOSE->OPEN->CONN）。|进入**重连状态机**，延时后重试。避免在循环中直接重发CONNECT指令导致模块或服务器阻塞。|
|2|发送 PINGREQ 包超时或失败|检查网络信号。执行完整的重连流程。|同上。通常预示着网络链路不稳定。|
|3|发送 CONNECT 包超时或失败|检查客户端ID是否被占用，或用户名密码是否正确。|检查配置后，进入**重连状态机**。|
|4|接收 CONNACK 包超时或失败|检查网络或服务器状态。|进入**重连状态机**。|
|5|客户端主动发送 `DISCONNECT`|这是正常流程，无需处理。|进入**空闲状态**或准备关闭网络。|
|6|因发送数据包总是失败，客户端主动断开|检查网络质量。|进入**重连状态机**。|

### 5.3 `+QMTPING`: 心跳状态变化

- **交互说明**: 当心跳包（PINGREQ）发送失败时上报。通常预示着网络链路出现问题。
  
- **URC格式与参数说明**:
  
    ```
    +QMTPING: <client_idx>,<result>
    ```
    
|   |   |   |   |
|---|---|---|---|
|**参数**|**释义**|**类型/格式**|**说明**|
|`<client_idx>`|客户端标识符索引|整型|范围 0-5。|
|`<result>`|Ping的状态结果|整型|`1` 表示失败。|
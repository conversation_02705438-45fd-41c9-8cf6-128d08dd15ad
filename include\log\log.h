#ifndef LOG_H
#define LOG_H

#include "logger_global.h"
#include "LogInfo.h"
#include "LogInstance.h"
#include "LogManager.h"
#include "logger.h"

#define LOG1 0

static Logging::TimeConfigInfo logInfo1 =
{
    "log/",
    Logging::BACKUP_BY_DAY,
    Logging::TRACE,
    512 * 1024 * 1024,
    0.2,
    ".log",
    LOG1,
};

#define logTrace(strTrace) LOG_TRACE(LOG1) << strTrace
#define logDebug(strDebug) LOG_DEBUG(LOG1) << strDebug
#define logInfo(strInfo) LOG_INFO(LOG1) << strInfo
#define logWarnning(strWarnning) LOG_WARN(LOG1) << strWarnning
#define logError(strError) LOG_ERROR(LOG1) << strError
#define logFatal(strFatal) LOG_FATAL(LOG1) << strFatal

#define traceLog() LOG_TRACE(LOG1)
#define debugLog() LOG_DEBUG(LOG1)
#define infoLog() LOG_INFO(LOG1)
#define warningLog() LOG_WARN(LOG1)
#define errorLog() LOG_ERROR(LOG1)
#define fatalLog() LOG_FATAL(LOG1)

#endif // LOG_H
